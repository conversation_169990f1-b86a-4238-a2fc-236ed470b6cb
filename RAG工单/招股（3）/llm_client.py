import openai
import logging

# 全局LLM客户端
llm_client = None


def init_llm_client():
    global llm_client
    try:
        llm_client = openai.OpenAI(
            api_key="ms-6f7de516-7098-44af-969d-b1d28d65ec00",
            base_url="https://api-inference.modelscope.cn/v1/",
            timeout=30
        )
        return llm_client
    except Exception as e:
        logging.error(f"LLM client initialization error: {str(e)}")
        raise


def format_chat_history(history, max_context_length=3):
    """格式化对话历史，保留最近的几轮对话作为上下文"""
    if not history:
        return []

    # 只保留最近的几轮对话
    recent_history = history[-max_context_length*2:] if len(history) > max_context_length*2 else history

    formatted_messages = []
    for msg in recent_history:
        if msg.get("role") in ["user", "assistant"]:
            formatted_messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })

    return formatted_messages


def generate_answer(query, context, client, chat_history=None):
    """生成答案，支持多轮对话上下文"""
    try:
        # 构建上下文信息
        context_text = "\n\n".join([item["text"] for item in context])

        # 构建消息列表
        messages = [
            {
                "role": "system",
                "content": """你是一个智能文档问答助手，具有以下特点：
1. 基于提供的文档上下文回答问题，确保答案准确可靠
2. 如果上下文信息不足，会诚实告知并建议用户提供更多信息
3. 能够理解对话历史，提供连贯的多轮对话体验
4. 回答简洁明了，重点突出，逻辑清晰
5. 对于复杂问题，会分步骤解答

请根据文档上下文和对话历史回答用户问题。"""
            }
        ]

        # 添加对话历史（如果存在）
        if chat_history:
            formatted_history = format_chat_history(chat_history, max_context_length=3)
            messages.extend(formatted_history)

        # 添加当前问题和上下文
        current_message = f"""文档上下文：
{context_text}

用户问题：{query}

请基于上述文档内容回答问题。如果需要结合之前的对话内容，请保持回答的连贯性。"""

        messages.append({
            "role": "user",
            "content": current_message
        })

        response = client.chat.completions.create(
            model="Qwen/Qwen2.5-Coder-32B-Instruct",
            messages=messages,
            temperature=0.7,
            max_tokens=1500,
            stream=False
        )

        return response.choices[0].message.content
    except Exception as e:
        logging.error(f"Error generating answer: {str(e)}")
        return f"生成回答时出错: {str(e)}"


def generate_answer(query, context, client, chat_history=[]):
    try:
        # 构建系统消息
        messages = [
            {
                "role": "system",
                "content": "你是一个AI助手，基于上传的文档内容回答用户问题。使用Markdown格式组织内容。"
            }
        ]

        # 添加历史对话
        messages.extend(chat_history)

        # 添加上下文
        context_text = "\n\n".join([item["text"] for item in context])
        if len(context_text) > 3000:
            context_text = context_text[:3000] + "...[内容已截断]"

        messages.append({
            "role": "system",
            "content": f"文档上下文信息：\n{context_text}"
        })

        # 添加当前查询
        messages.append({
            "role": "user",
            "content": query
        })

        response = client.chat.completions.create(
            model="Qwen/Qwen2.5-Coder-32B-Instruct",
            messages=messages,
            stream=False,
            timeout=20
        )

        return response.choices[0].message.content.strip()

    except openai.RateLimitError:
        logging.error("API请求达到速率限制")
        return "抱歉，当前请求过于频繁，请稍后再试。"
    except Exception as e:
        logging.error(f"生成答案错误: {str(e)}")
        return "生成答案时出现错误，请稍后再试。"