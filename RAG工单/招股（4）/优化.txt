多轮对话
语音聊天
命名体识别（Query）
文件处理（图片，表格，文本）
文档分割语义连贯
HNSW + 混合检索（提升向量数据库的速度）


Qwen/Qwen3-Coder-480B-A35B-Instruct
sglang


对招股（4）下的代码功能：1：实现多轮对话，聊天记录保存在Redis中
2：实现语音聊天，语音转文字，文字转语音
3：实现对用户的问题进行命名体识别，对问题进行分类，匹配向量数据库相似，替换大模型的提示词回答
3：优化文件处理，包括对图片，表格，文本的识别保存在向量数据库
4：优化文档分割，第一保持语义连贯，联系上下文，第二保持文档，表格的完整性，第三实现重叠，第四提高处理速度
5：优化向量数据库的检索，混合检索，提升向量数据库的检索速度和准确率，
6：优化前端，保证前端对应的后端功能齐全，方便用户实现RAG检索功能，并且可视化参考的文档和相关度，
7：保证这段代码的健壮性，容错性，可扩展性，可维护性，对每行代码进行注释

