# -*- coding: utf-8 -*-
"""
命名实体识别和意图分类模块
实现对用户问题的实体提取和意图分类，用于优化检索和生成
"""

import re
import logging
import jieba
import jieba.posseg as pseg
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict
import json

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class Entity:
    """实体类"""
    text: str  # 实体文本
    label: str  # 实体标签
    start: int  # 开始位置
    end: int  # 结束位置
    confidence: float = 1.0  # 置信度

@dataclass
class Intent:
    """意图类"""
    label: str  # 意图标签
    confidence: float  # 置信度
    keywords: List[str] = None  # 关键词

class ChineseNER:
    """中文命名实体识别器"""
    
    def __init__(self):
        """初始化NER模型"""
        self.logger = logging.getLogger(__name__)
        
        # 实体识别规则和词典
        self._init_entity_patterns()
        self._init_entity_dictionaries()
        
        # 加载jieba用户词典
        self._load_custom_dict()
        
    def _init_entity_patterns(self):
        """初始化实体识别正则模式"""
        self.patterns = {
            'MONEY': [
                r'(\d+(?:\.\d+)?)\s*(?:万|千|百)?(?:元|块|角|分|美元|港币|人民币)',
                r'(?:约|大约|超过|不超过|至少)?\s*(\d+(?:\.\d+)?)\s*(?:万|千|百)?\s*(?:元|块|美元)',
                r'(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*(?:元|美元|港币)',
            ],
            'PERCENT': [
                r'(\d+(?:\.\d+)?)\s*%',
                r'百分之\s*(\d+(?:\.\d+)?)',
                r'(\d+(?:\.\d+)?)\s*个百分点',
            ],
            'DATE': [
                r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日',
                r'(\d{4})\s*年\s*(\d{1,2})\s*月',
                r'(\d{4})\s*年',
                r'(\d{1,2})\s*月\s*(\d{1,2})\s*日',
                r'(今年|去年|明年|前年|后年)',
                r'(今天|昨天|明天|前天|后天)',
            ],
            'TIME': [
                r'(\d{1,2})\s*点\s*(\d{1,2})\s*分',
                r'(\d{1,2})\s*点',
                r'(上午|下午|中午|晚上|凌晨)',
            ],
            'QUANTITY': [
                r'(\d+(?:\.\d+)?)\s*(?:个|只|件|台|套|批|次|倍|万|千|百)',
                r'第\s*(\d+)\s*(?:个|只|件|台|套|批|次)',
            ]
        }
        
    def _init_entity_dictionaries(self):
        """初始化实体词典"""
        self.entity_dicts = {
            'ORG_SUFFIX': ['公司', '集团', '企业', '机构', '组织', '协会', '基金会', '研究院', '大学', '学院'],
            'PERSON_TITLE': ['董事长', '总经理', 'CEO', 'CFO', 'CTO', '主席', '部长', '经理', '总监', '主任'],
            'LOCATION_SUFFIX': ['省', '市', '县', '区', '镇', '村', '街道', '路', '街', '巷'],
            'PRODUCT_KEYWORDS': ['产品', '服务', '项目', '业务', '系统', '平台', '软件', '硬件', '设备'],
        }
        
    def _load_custom_dict(self):
        """加载自定义词典"""
        try:
            # 这里可以加载自定义的行业词典
            # jieba.load_userdict('custom_dict.txt')
            pass
        except Exception as e:
            self.logger.warning(f"加载自定义词典失败: {e}")
    
    def extract_entities(self, text: str) -> List[Entity]:
        """提取命名实体"""
        entities = []
        
        # 1. 基于正则表达式的实体提取
        entities.extend(self._extract_pattern_entities(text))
        
        # 2. 基于词性标注的实体提取
        entities.extend(self._extract_pos_entities(text))
        
        # 3. 基于规则的实体提取
        entities.extend(self._extract_rule_entities(text))
        
        # 4. 去重和排序
        entities = self._deduplicate_entities(entities)
        
        return entities
    
    def _extract_pattern_entities(self, text: str) -> List[Entity]:
        """基于正则模式提取实体"""
        entities = []
        
        for entity_type, patterns in self.patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text)
                for match in matches:
                    entity = Entity(
                        text=match.group(),
                        label=entity_type,
                        start=match.start(),
                        end=match.end(),
                        confidence=0.9
                    )
                    entities.append(entity)
        
        return entities
    
    def _extract_pos_entities(self, text: str) -> List[Entity]:
        """基于词性标注提取实体"""
        entities = []
        
        # 使用jieba进行词性标注
        words = pseg.cut(text)
        offset = 0
        
        for word, flag in words:
            start_pos = text.find(word, offset)
            end_pos = start_pos + len(word)
            
            # 根据词性标注判断实体类型
            entity_type = self._pos_to_entity_type(word, flag)
            if entity_type:
                entity = Entity(
                    text=word,
                    label=entity_type,
                    start=start_pos,
                    end=end_pos,
                    confidence=0.8
                )
                entities.append(entity)
            
            offset = end_pos
        
        return entities
    
    def _pos_to_entity_type(self, word: str, pos: str) -> Optional[str]:
        """根据词性标注判断实体类型"""
        # 人名
        if pos == 'nr':
            return 'PERSON'
        # 地名
        elif pos == 'ns':
            return 'LOCATION'
        # 机构名
        elif pos == 'nt':
            return 'ORGANIZATION'
        # 时间
        elif pos == 't':
            return 'TIME'
        # 数词
        elif pos == 'm' and re.search(r'\d', word):
            return 'NUMBER'
        
        return None
    
    def _extract_rule_entities(self, text: str) -> List[Entity]:
        """基于规则提取实体"""
        entities = []
        
        # 组织机构识别
        for suffix in self.entity_dicts['ORG_SUFFIX']:
            pattern = rf'[\u4e00-\u9fa5]+{suffix}'
            matches = re.finditer(pattern, text)
            for match in matches:
                if len(match.group()) > len(suffix) + 1:  # 避免只匹配后缀
                    entity = Entity(
                        text=match.group(),
                        label='ORGANIZATION',
                        start=match.start(),
                        end=match.end(),
                        confidence=0.7
                    )
                    entities.append(entity)
        
        # 人名识别（基于职位）
        for title in self.entity_dicts['PERSON_TITLE']:
            pattern = rf'[\u4e00-\u9fa5]{{2,4}}{title}'
            matches = re.finditer(pattern, text)
            for match in matches:
                person_name = match.group().replace(title, '')
                if len(person_name) >= 2:
                    entity = Entity(
                        text=person_name,
                        label='PERSON',
                        start=match.start(),
                        end=match.start() + len(person_name),
                        confidence=0.6
                    )
                    entities.append(entity)
        
        return entities
    
    def _deduplicate_entities(self, entities: List[Entity]) -> List[Entity]:
        """去重和合并重叠实体"""
        if not entities:
            return []
        
        # 按位置排序
        entities.sort(key=lambda x: (x.start, x.end))
        
        # 去重
        unique_entities = []
        for entity in entities:
            # 检查是否与已有实体重叠
            is_duplicate = False
            for existing in unique_entities:
                if (entity.start < existing.end and entity.end > existing.start):
                    # 重叠实体，保留置信度更高的
                    if entity.confidence > existing.confidence:
                        unique_entities.remove(existing)
                        unique_entities.append(entity)
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_entities.append(entity)
        
        return unique_entities

class IntentClassifier:
    """意图分类器"""
    
    def __init__(self):
        """初始化意图分类器"""
        self.logger = logging.getLogger(__name__)
        
        # 意图关键词映射
        self.intent_keywords = {
            'question_answering': [
                '什么', '如何', '怎么', '为什么', '哪个', '哪些', '谁', '何时', '何地',
                '是什么', '是谁', '是哪', '怎样', '如何做', '什么意思', '解释', '说明'
            ],
            'information_retrieval': [
                '查找', '搜索', '寻找', '找到', '获取', '得到', '查询', '检索',
                '显示', '列出', '给出', '提供', '告诉我', '我想知道'
            ],
            'document_summary': [
                '总结', '概括', '摘要', '归纳', '梳理', '整理', '汇总',
                '主要内容', '核心', '要点', '重点', '关键信息'
            ],
            'data_analysis': [
                '分析', '统计', '计算', '比较', '对比', '评估', '评价',
                '趋势', '变化', '增长', '下降', '占比', '比例', '数据'
            ],
            'comparison': [
                '比较', '对比', '差异', '区别', '相同', '不同', '优劣',
                '哪个更好', '哪个更', '与', '和', '相比', '对比分析'
            ]
        }
        
        # 问句模式
        self.question_patterns = [
            r'.*[？?]$',  # 以问号结尾
            r'^(什么|如何|怎么|为什么|哪个|哪些|谁|何时|何地)',  # 疑问词开头
            r'(吗|呢)[？?]?$',  # 疑问语气词结尾
        ]
    
    def classify_intent(self, text: str) -> Intent:
        """分类用户意图"""
        # 预处理文本
        text = text.strip().lower()
        
        # 计算每个意图的得分
        intent_scores = defaultdict(float)
        
        # 1. 基于关键词匹配
        for intent, keywords in self.intent_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    intent_scores[intent] += 1.0
        
        # 2. 基于问句模式
        is_question = any(re.search(pattern, text) for pattern in self.question_patterns)
        if is_question:
            intent_scores['question_answering'] += 2.0
        
        # 3. 基于特殊模式
        if re.search(r'(总结|概括|摘要)', text):
            intent_scores['document_summary'] += 2.0
        
        if re.search(r'(比较|对比|差异)', text):
            intent_scores['comparison'] += 2.0
        
        if re.search(r'(分析|统计|数据)', text):
            intent_scores['data_analysis'] += 2.0
        
        # 4. 确定最终意图
        if intent_scores:
            best_intent = max(intent_scores.items(), key=lambda x: x[1])
            confidence = min(best_intent[1] / 5.0, 1.0)  # 归一化置信度
            
            return Intent(
                label=best_intent[0],
                confidence=confidence,
                keywords=self._extract_intent_keywords(text, best_intent[0])
            )
        else:
            return Intent(
                label='other',
                confidence=0.5,
                keywords=[]
            )
    
    def _extract_intent_keywords(self, text: str, intent: str) -> List[str]:
        """提取与意图相关的关键词"""
        keywords = []
        
        if intent in self.intent_keywords:
            for keyword in self.intent_keywords[intent]:
                if keyword in text:
                    keywords.append(keyword)
        
        return keywords

class EnhancedNERClassifier:
    """增强的NER和意图分类器"""
    
    def __init__(self):
        """初始化增强分类器"""
        self.ner = ChineseNER()
        self.intent_classifier = IntentClassifier()
        self.logger = logging.getLogger(__name__)
    
    def analyze_query(self, query: str) -> Dict:
        """分析查询，返回实体和意图信息"""
        try:
            # 提取命名实体
            entities = self.ner.extract_entities(query)
            
            # 分类意图
            intent = self.intent_classifier.classify_intent(query)
            
            # 构建分析结果
            result = {
                'query': query,
                'entities': [
                    {
                        'text': entity.text,
                        'label': entity.label,
                        'start': entity.start,
                        'end': entity.end,
                        'confidence': entity.confidence
                    }
                    for entity in entities
                ],
                'intent': {
                    'label': intent.label,
                    'confidence': intent.confidence,
                    'keywords': intent.keywords or []
                },
                'entity_summary': self._summarize_entities(entities),
                'query_type': self._determine_query_type(intent, entities)
            }
            
            self.logger.info(f"查询分析完成: {query[:50]}... -> 意图: {intent.label}, 实体数: {len(entities)}")
            return result
            
        except Exception as e:
            self.logger.error(f"查询分析失败: {e}", exc_info=True)
            return {
                'query': query,
                'entities': [],
                'intent': {'label': 'other', 'confidence': 0.0, 'keywords': []},
                'entity_summary': {},
                'query_type': 'general'
            }
    
    def _summarize_entities(self, entities: List[Entity]) -> Dict:
        """汇总实体信息"""
        summary = defaultdict(list)
        
        for entity in entities:
            summary[entity.label].append(entity.text)
        
        return dict(summary)
    
    def _determine_query_type(self, intent: Intent, entities: List[Entity]) -> str:
        """确定查询类型"""
        # 基于意图和实体确定查询类型
        if intent.label == 'question_answering':
            if any(e.label in ['PERSON', 'ORGANIZATION'] for e in entities):
                return 'entity_question'
            elif any(e.label in ['MONEY', 'PERCENT', 'QUANTITY'] for e in entities):
                return 'numeric_question'
            else:
                return 'general_question'
        
        elif intent.label == 'data_analysis':
            return 'analytical'
        
        elif intent.label == 'comparison':
            return 'comparative'
        
        elif intent.label == 'document_summary':
            return 'summary'
        
        else:
            return 'general'
    
    def generate_enhanced_prompt(self, query: str, analysis_result: Dict, context: str = "") -> str:
        """基于分析结果生成增强的提示词"""
        intent = analysis_result['intent']
        entities = analysis_result['entity_summary']
        query_type = analysis_result['query_type']
        
        # 基础提示词
        base_prompt = f"用户问题：{query}\n\n"
        
        # 根据意图调整提示词
        if intent['label'] == 'question_answering':
            base_prompt += "请基于提供的文档内容，准确回答用户的问题。"
            if entities:
                base_prompt += f"\n注意关注以下关键实体：{', '.join([f'{k}: {v}' for k, v in entities.items()])}"
        
        elif intent['label'] == 'document_summary':
            base_prompt += "请对相关文档内容进行总结和概括，突出要点。"
        
        elif intent['label'] == 'data_analysis':
            base_prompt += "请对相关数据进行分析，提供具体的数字和趋势信息。"
        
        elif intent['label'] == 'comparison':
            base_prompt += "请进行对比分析，明确指出差异和相似之处。"
        
        else:
            base_prompt += "请基于文档内容提供准确、有用的信息。"
        
        # 添加上下文
        if context:
            base_prompt += f"\n\n参考文档：\n{context}"
        
        return base_prompt

# 全局实例
ner_classifier = EnhancedNERClassifier()

def analyze_user_query(query: str) -> Dict:
    """分析用户查询的便捷函数"""
    return ner_classifier.analyze_query(query)

def generate_enhanced_prompt(query: str, context: str = "") -> str:
    """生成增强提示词的便捷函数"""
    analysis = analyze_user_query(query)
    return ner_classifier.generate_enhanced_prompt(query, analysis, context)
