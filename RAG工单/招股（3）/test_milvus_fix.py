#!/usr/bin/env python3
"""
测试Milvus修复的脚本
用于验证schema兼容性和向量存储功能
"""

import sys
import os
import logging
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_milvus_initialization():
    """测试Milvus初始化"""
    try:
        from milvus_manager import init_milvus, reset_collection
        
        logger.info("开始测试Milvus初始化...")
        
        # 测试初始化
        collection = init_milvus()
        logger.info(f"Milvus初始化成功，集合: {collection.name}")
        
        # 检查schema
        schema_fields = [field.name for field in collection.schema.fields]
        logger.info(f"集合schema字段: {schema_fields}")
        
        expected_fields = ["id", "text", "vector", "chunk_type", "has_table", "has_figure", "semantic_level", "chunk_length", "metadata_json"]
        missing_fields = [field for field in expected_fields if field not in schema_fields]
        
        if missing_fields:
            logger.warning(f"缺少字段: {missing_fields}")
            logger.info("尝试重置集合...")
            reset_result = reset_collection()
            if reset_result["status"] == "success":
                logger.info("集合重置成功")
                collection = reset_result["collection"]
                schema_fields = [field.name for field in collection.schema.fields]
                logger.info(f"新集合schema字段: {schema_fields}")
            else:
                logger.error(f"集合重置失败: {reset_result['message']}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"Milvus初始化测试失败: {str(e)}")
        return False

def test_vector_storage():
    """测试向量存储功能"""
    try:
        from milvus_manager import init_milvus, store_vectors_with_metadata, store_vectors
        
        logger.info("开始测试向量存储...")
        
        # 初始化集合
        collection = init_milvus()
        
        # 准备测试数据
        test_chunks = [
            {
                "text": "这是一个测试文本块1",
                "metadata": {
                    "type": "text",
                    "has_table": False,
                    "has_figure": False,
                    "semantic_level": "detail",
                    "length": 10
                }
            },
            {
                "text": "这是一个包含表格的测试文本块2",
                "metadata": {
                    "type": "mixed",
                    "has_table": True,
                    "has_figure": False,
                    "semantic_level": "summary",
                    "length": 15
                }
            }
        ]
        
        # 生成测试向量（1024维）
        test_embeddings = np.random.random((len(test_chunks), 1024)).astype(np.float32)
        
        # 测试带元数据的存储
        logger.info("测试带元数据的向量存储...")
        result = store_vectors_with_metadata(test_chunks, test_embeddings, collection)
        
        if result["status"] == "success":
            logger.info(f"带元数据存储成功，存储了 {result['count']} 个向量")
        else:
            logger.error(f"带元数据存储失败: {result['message']}")
            return False
        
        # 测试原始存储（向后兼容）
        logger.info("测试原始向量存储...")
        test_texts = ["测试文本1", "测试文本2"]
        test_embeddings_simple = np.random.random((len(test_texts), 1024)).astype(np.float32)
        
        result = store_vectors(test_texts, test_embeddings_simple, collection)
        
        if result["status"] == "success":
            logger.info(f"原始存储成功，存储了 {result['count']} 个向量")
        else:
            logger.error(f"原始存储失败: {result['message']}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"向量存储测试失败: {str(e)}")
        return False

def test_search_functionality():
    """测试搜索功能"""
    try:
        from milvus_manager import init_milvus, search_with_metadata_filter
        
        logger.info("开始测试搜索功能...")
        
        # 初始化集合
        collection = init_milvus()
        
        # 生成查询向量
        query_vector = np.random.random(1024).astype(np.float32).tolist()
        
        # 测试基础搜索
        results = search_with_metadata_filter(
            collection=collection,
            query_vector=query_vector,
            top_k=5
        )
        
        logger.info(f"搜索完成，返回 {len(results)} 个结果")
        
        # 测试带过滤的搜索
        filter_results = search_with_metadata_filter(
            collection=collection,
            query_vector=query_vector,
            filter_expr="has_table == true",
            top_k=3
        )
        
        logger.info(f"过滤搜索完成，返回 {len(filter_results)} 个结果")
        
        return True
        
    except Exception as e:
        logger.error(f"搜索功能测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("开始Milvus修复验证测试...")
    
    tests = [
        ("Milvus初始化", test_milvus_initialization),
        ("向量存储", test_vector_storage),
        ("搜索功能", test_search_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {str(e)}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试总结: {passed}/{total} 个测试通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！Milvus修复成功！")
        return True
    else:
        logger.error("⚠️  部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
