import os
import uuid
import logging
import time
from flask import Flask, request, jsonify, render_template, session
from flask_cors import CORS

# 导入配置和错误处理
try:
    from config import get_config
except ImportError:
    get_config = None

try:
    from error_handler import (
        error_handler, handle_exceptions, log_execution_time,
        monitor_performance, log_info, log_warning, log_error
    )
except ImportError:
    # 提供基本的日志函数
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    def handle_exceptions(fallback_return=None):
        def decorator(func):
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger.error(f"函数 {func.__name__} 执行失败: {e}")
                    return fallback_return
            return wrapper
        return decorator

    def log_execution_time(func):
        return func

    def monitor_performance(func):
        return func

    def log_info(msg, context=None):
        logger.info(msg)

    def log_warning(msg, context=None):
        logger.warning(msg)

    def log_error(error, context=None):
        logger.error(f"错误: {error}")

try:
    from system_monitor import (
        system_monitor, start_system_monitoring,
        check_component_health, get_system_status
    )
except ImportError:
    # 提供基本的监控函数
    def start_system_monitoring(*args, **kwargs):
        pass

    def check_component_health(name, func):
        try:
            func()
            return {"name": name, "status": "healthy"}
        except:
            return {"name": name, "status": "error"}

    def get_system_status():
        return {"status": "basic_mode"}

# 导入核心模块
from file_processor import allowed_file
try:
    from file_processor import process_uploaded_file_enhanced
except ImportError:
    from file_processor import process_uploaded_file as process_uploaded_file_enhanced

try:
    from vector_search import search_similar_enhanced
except ImportError:
    from vector_search import search_similar as search_similar_enhanced

from llm_client import generate_answer
try:
    from llm_client import generate_answer_with_prompt
except ImportError:
    generate_answer_with_prompt = generate_answer

from embeddings import generate_embeddings, init_embedding_model
from milvus_manager import init_milvus, store_vectors
try:
    from milvus_manager import get_collection_stats
except ImportError:
    def get_collection_stats(collection):
        return {"num_entities": 0, "status": "unknown"}

from llm_client import init_llm_client
from reranker import rerank_documents
from redis_manager import redis_manager

# 导入增强功能模块
try:
    from speech_processor import get_speech_processor
    SPEECH_ENABLED = True
except ImportError as e:
    SPEECH_ENABLED = False
    print(f"语音处理模块导入失败，语音功能将被禁用: {e}")

try:
    from ner_classifier import get_ner_classifier
    NER_ENABLED = True
except ImportError as e:
    NER_ENABLED = False
    print(f"NER分类模块导入失败，智能分类功能将被禁用: {e}")

try:
    from vector_search import get_hybrid_search_engine
    HYBRID_SEARCH_ENABLED = True
except ImportError as e:
    HYBRID_SEARCH_ENABLED = False
    print(f"混合检索模块导入失败，将使用基础向量检索: {e}")

# 获取配置
try:
    config = get_config()
except Exception as e:
    print(f"配置加载失败，使用默认配置: {e}")
    # 使用基本配置
    class BasicConfig:
        SECRET_KEY = 'your-secret-key-here'
        UPLOAD_FOLDER = 'uploads'
        MAX_CONTENT_LENGTH = 50 * 1024 * 1024
        ALLOWED_EXTENSIONS = {'txt', 'pdf', 'docx', 'doc', 'jpg', 'jpeg', 'png'}
        HOST = '0.0.0.0'
        PORT = 5000
        DEBUG = True
        APP_NAME = "智能文档问答系统"
        APP_VERSION = "2.0.0"
        HYBRID_SEARCH_ENABLED = False
        NER_ENABLED = False
        HEALTH_CHECK_ENABLED = False
        ENABLE_DETAILED_LOGGING = False
        VECTOR_SEARCH_TOP_K = 10
        RERANKER_TOP_N = 5
        LLM_MODEL_NAME = "Qwen2.5-Coder-32B"

    config = BasicConfig()

# 创建Flask应用
app = Flask(__name__)
app.secret_key = config.SECRET_KEY
CORS(app)

# 应用配置
app.config['UPLOAD_FOLDER'] = config.UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = config.MAX_CONTENT_LENGTH

# 确保必要目录存在
os.makedirs(config.UPLOAD_FOLDER, exist_ok=True)
os.makedirs("static/audio", exist_ok=True)
os.makedirs("logs", exist_ok=True)

# 配置日志（使用错误处理模块的日志配置）
logger = logging.getLogger(__name__)
log_info(f"应用启动 - {config.APP_NAME} v{config.APP_VERSION}")

# 全局组件
collection = None
embedding_model = None
llm_client = None
speech_processor = None
ner_classifier = None
hybrid_search_engine = None

# 组件健康检查函数
def check_milvus_health():
    """检查Milvus健康状态"""
    if collection is None:
        raise Exception("Milvus集合未初始化")
    return get_collection_stats(collection)

def check_redis_health():
    """检查Redis健康状态"""
    if redis_manager is None:
        raise Exception("Redis管理器未初始化")
    info = redis_manager.connection.info()
    return {"connected_clients": info.get("connected_clients", 0)}

def check_llm_health():
    """检查LLM健康状态"""
    if llm_client is None:
        raise Exception("LLM客户端未初始化")
    return {"status": "connected"}

@handle_exceptions(fallback_return=False)
@log_execution_time
def initialize_application():
    """
    初始化应用程序所有组件
    包括向量数据库、嵌入模型、LLM客户端、语音处理器、NER分类器等

    Returns:
        bool: 初始化是否成功
    """
    global collection, embedding_model, llm_client, speech_processor, ner_classifier, hybrid_search_engine

    log_info("正在初始化应用程序组件...")

    # 验证配置
    try:
        if hasattr(config, 'validate_config'):
            config_errors = config.validate_config()
            if config_errors:
                for error in config_errors:
                    log_error(Exception(f"配置错误: {error}"))
                return False
    except Exception as e:
        log_warning(f"配置验证失败: {e}")

    # 初始化核心组件
    try:
        collection = init_milvus()
        log_info("Milvus向量数据库初始化成功")
    except Exception as e:
        log_error(e, {"component": "milvus"})
        return False

    try:
        embedding_model = init_embedding_model()
        log_info("嵌入模型初始化成功")
    except Exception as e:
        log_error(e, {"component": "embedding_model"})
        return False

    try:
        llm_client = init_llm_client()
        log_info("LLM客户端初始化成功")
    except Exception as e:
        log_error(e, {"component": "llm_client"})
        return False

    # 初始化增强功能组件（可选）
    if SPEECH_ENABLED:
        try:
            speech_processor = get_speech_processor()
            log_info("语音处理器初始化成功")
        except Exception as e:
            log_warning(f"语音处理器初始化失败: {str(e)}")
            speech_processor = None

    if NER_ENABLED:
        try:
            ner_classifier = get_ner_classifier()
            log_info("NER分类器初始化成功")
        except Exception as e:
            log_warning(f"NER分类器初始化失败: {str(e)}")
            ner_classifier = None

    if HYBRID_SEARCH_ENABLED:
        try:
            hybrid_search_engine = get_hybrid_search_engine()
            log_info("混合检索引擎初始化成功")
        except Exception as e:
            log_warning(f"混合检索引擎初始化失败: {str(e)}")
            hybrid_search_engine = None

    # 启动系统监控
    try:
        if hasattr(config, 'HEALTH_CHECK_ENABLED') and config.HEALTH_CHECK_ENABLED:
            interval = getattr(config, 'HEALTH_CHECK_INTERVAL', 60)
            start_system_monitoring(interval)
            log_info("系统监控已启动")
    except Exception as e:
        log_warning(f"系统监控启动失败: {e}")

    # 注册组件健康检查
    check_component_health("milvus", check_milvus_health)
    check_component_health("redis", check_redis_health)
    check_component_health("llm", check_llm_health)

    log_info("所有组件初始化完成")
    return True


# 在应用启动前初始化组件
initialize_application()


# 路由
@app.route('/')
def index():
    # 为新访客生成会话ID
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
        logger.info(f"新会话开始: {session['session_id']}")
    return render_template('index.html')


@app.route('/upload', methods=['POST'])
@handle_exceptions(fallback_return=None)
@log_execution_time
@monitor_performance
def upload_file():
    """
    处理文件上传
    支持文档、图片、表格、音频等多种文件类型

    Returns:
        JSON响应，包含处理结果
    """
    try:
        # 检查文件是否存在
        if 'file' not in request.files:
            return jsonify({"status": "error", "message": "没有文件部分"})

        file = request.files['file']
        if file.filename == '':
            return jsonify({"status": "error", "message": "没有选择文件"})

        # 检查文件类型
        if not file or not allowed_file(file.filename, config.ALLOWED_EXTENSIONS):
            return jsonify({
                "status": "error",
                "message": f"不支持的文件类型。支持的类型: {', '.join(sorted(config.ALLOWED_EXTENSIONS))}"
            })

        # 检查文件大小
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置到文件开头

        if file_size > config.MAX_CONTENT_LENGTH:
            return jsonify({
                "status": "error",
                "message": f"文件过大，最大支持 {config.MAX_CONTENT_LENGTH // (1024*1024)}MB"
            })

        log_info(f"开始处理文件上传: {file.filename}, 大小: {file_size} bytes")

        # 获取文件类型
        from file_processor import EnhancedFileProcessor
        processor = EnhancedFileProcessor()
        file_type = processor.get_file_type(file.filename)

        # 处理音频文件
        if file_type == 'unknown' and speech_processor and hasattr(processor, 'is_audio_file') and processor.is_audio_file(file.filename):
            return handle_audio_upload(file, file_size)

        # 处理其他文件类型
        try:
            # 使用增强的文件处理
            result = process_uploaded_file_enhanced(
                file,
                config.UPLOAD_FOLDER,
                collection,
                generate_embeddings
            )

            # 添加文件信息到响应
            result['file_info'] = {
                'filename': file.filename,
                'file_type': file_type,
                'file_size': file_size,
                'processing_method': 'enhanced'
            }

            log_info(f"文件处理成功: {file.filename}, 生成 {result.get('chunks_count', 0)} 个文本块")
            return jsonify(result)

        except Exception as e:
            log_warning(f"增强文件处理失败，尝试基础处理: {str(e)}")

            # 回退到基础处理
            try:
                from file_processor import process_uploaded_file
                result = process_uploaded_file(
                    file,
                    config.UPLOAD_FOLDER,
                    collection,
                    generate_embeddings
                )
                result['processing_method'] = 'fallback'
                result['file_info'] = {
                    'filename': file.filename,
                    'file_type': file_type,
                    'file_size': file_size
                }

                log_info(f"文件基础处理成功: {file.filename}")
                return jsonify(result)

            except Exception as fallback_error:
                log_error(fallback_error, {"filename": file.filename, "file_size": file_size})
                return jsonify({
                    "status": "error",
                    "message": f"处理文件时出错: {str(fallback_error)}"
                })

    except Exception as e:
        log_error(e, {"context": "文件上传处理"})
        return jsonify({"status": "error", "message": "文件上传处理失败"})

@handle_exceptions(fallback_return=None)
def handle_audio_upload(file, file_size):
    """
    处理音频文件上传

    Args:
        file: 音频文件对象
        file_size: 文件大小

    Returns:
        JSON响应
    """
    try:
        if not speech_processor:
            return jsonify({
                "status": "error",
                "message": "语音处理功能未启用"
            })

        log_info(f"开始处理音频文件: {file.filename}")

        # 保存临时文件
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            file.save(temp_file.name)

            try:
                # 语音转文字
                text = speech_processor.speech_to_text(temp_file.name)
            finally:
                # 确保删除临时文件
                os.unlink(temp_file.name)

        if not text:
            return jsonify({
                "status": "error",
                "message": "语音识别失败，无法提取文字内容"
            })

        log_info(f"语音识别成功，提取文字长度: {len(text)}")

        # 将识别的文字作为文本处理
        from file_processor import SmartTextChunker
        chunker = SmartTextChunker()
        chunks_with_metadata = chunker.chunk_text_smart(text)

        # 提取纯文本用于嵌入
        chunk_texts = [chunk["text"] for chunk in chunks_with_metadata]

        # 生成嵌入向量
        embeddings = generate_embeddings(chunk_texts)

        # 存储向量
        from milvus_manager import store_vectors_with_metadata
        try:
            ids = store_vectors_with_metadata(chunks_with_metadata, embeddings, collection)
        except:
            from milvus_manager import store_vectors
            ids = store_vectors(chunk_texts, embeddings, collection)

        log_info(f"音频文件处理完成: {file.filename}, 生成 {len(chunks_with_metadata)} 个文本块")

        return jsonify({
            "status": "success",
            "message": f"音频文件处理成功，识别文字并存储 {len(chunks_with_metadata)} 个片段",
            "chunks_count": len(chunks_with_metadata),
            "file_type": "audio",
            "file_info": {
                "filename": file.filename,
                "file_size": file_size,
                "processing_method": "speech_recognition"
            },
            "recognized_text": text[:200] + "..." if len(text) > 200 else text
        })

    except Exception as e:
        log_error(e, {"context": "音频文件处理"})
        return jsonify({"status": "error", "message": "音频处理失败"})


@app.route('/query', methods=['POST'])
@handle_exceptions(fallback_return=None)
@log_execution_time
@monitor_performance
def handle_query():
    """
    处理用户查询
    集成NER分类、混合检索、智能重排序和增强回答生成

    Returns:
        JSON响应，包含答案和相关信息
    """
    try:
        # 解析请求数据
        data = request.json
        if not data or 'query' not in data:
            return jsonify({"status": "error", "message": "缺少查询内容"})

        # 获取会话ID
        if 'session_id' not in session:
            session['session_id'] = str(uuid.uuid4())
        session_id = session['session_id']

        # 获取历史对话
        chat_history = redis_manager.get_session(session_id)

        query_text = data['query'].strip()
        if not query_text:
            return jsonify({"status": "error", "message": "查询内容不能为空"})

        # 获取查询选项
        use_hybrid_search = data.get('use_hybrid_search', getattr(config, 'HYBRID_SEARCH_ENABLED', False))
        use_ner_classification = data.get('use_ner_classification', getattr(config, 'NER_ENABLED', False))
        enable_tts = data.get('enable_tts', False)

        log_info(f"处理查询: {query_text[:50]}...", {
            "session_id": session_id,
            "query_length": len(query_text),
            "use_hybrid_search": use_hybrid_search,
            "use_ner_classification": use_ner_classification
        })

        # 1. NER分析和问题分类
        query_analysis = None
        if ner_classifier and use_ner_classification:
            try:
                query_analysis = ner_classifier.analyze_query(query_text)
                log_info(f"查询分析完成: {query_analysis['classification']['category']}")
            except Exception as e:
                log_warning(f"NER分析失败: {str(e)}")

        # 2. 混合检索
        try:
            if hybrid_search_engine and use_hybrid_search:
                initial_results = hybrid_search_engine.hybrid_search(
                    query_text, collection, generate_embeddings, top_k=getattr(config, 'VECTOR_SEARCH_TOP_K', 10)
                )
                search_method = "hybrid"
            else:
                # 回退到基础向量检索
                initial_results = search_similar_enhanced(
                    query_text, collection, generate_embeddings,
                    top_k=getattr(config, 'VECTOR_SEARCH_TOP_K', 10), use_hybrid=False
                )
                search_method = "vector"

            log_info(f"使用{search_method}检索，获得{len(initial_results)}个初始结果")

        except Exception as e:
            log_error(e, {"query": query_text, "search_method": search_method})
            initial_results = []

        # 检查是否有搜索结果
        if not initial_results:
            no_result_message = "没有找到相关信息来回答这个问题。请先上传相关文件。"

            # 添加到历史对话
            new_history = chat_history + [
                {"role": "user", "content": query_text},
                {"role": "assistant", "content": no_result_message}
            ]
            redis_manager.store_session(session_id, new_history)

            return jsonify({
                "status": "success",
                "session_id": session_id,
                "answer": no_result_message,
                "context": [],
                "search_method": search_method,
                "result_count": 0,
                "query_analysis": query_analysis
            })

        # 3. 文档重排序
        try:
            reranked_results = rerank_documents(query_text, initial_results, top_n=getattr(config, 'RERANKER_TOP_N', 5))
            log_info(f"重排序完成，选择{len(reranked_results)}个最相关文档")
        except Exception as e:
            log_warning(f"重排序失败: {str(e)}")
            reranked_results = initial_results[:getattr(config, 'RERANKER_TOP_N', 5)]

        # 4. 生成增强答案
        try:
            if query_analysis and ner_classifier:
                # 使用增强的提示词
                enhanced_prompt = ner_classifier.generate_enhanced_prompt(
                    query_text, reranked_results, query_analysis['classification']
                )

                # 使用增强提示词生成答案
                try:
                    answer = generate_answer_with_prompt(enhanced_prompt, llm_client, chat_history)
                    answer_method = "enhanced"
                except Exception as e:
                    log_warning(f"增强提示词生成失败，回退到原始方法: {str(e)}")
                    answer = generate_answer(query_text, reranked_results, llm_client, chat_history)
                    answer_method = "fallback"
            else:
                # 使用原始方法
                answer = generate_answer(query_text, reranked_results, llm_client, chat_history)
                answer_method = "standard"

            log_info(f"答案生成完成，方法: {answer_method}")

        except Exception as e:
            log_error(e, {"query": query_text})
            answer = "抱歉，生成答案时出现错误，请稍后再试。"
            answer_method = "error"

        # 5. 语音合成（如果启用）
        audio_url = None
        if enable_tts and speech_processor:
            try:
                audio_filename = f"answer_{session_id}_{int(time.time())}.wav"
                audio_path = os.path.join("static", "audio", audio_filename)

                # 生成语音
                result_path = speech_processor.text_to_speech(answer, audio_path)
                if result_path:
                    audio_url = f"/static/audio/{audio_filename}"
                    log_info("语音合成完成")

            except Exception as e:
                log_warning(f"语音合成失败: {str(e)}")

        # 6. 更新历史对话
        new_history = chat_history + [
            {"role": "user", "content": query_text},
            {"role": "assistant", "content": answer}
        ]
        redis_manager.store_session(session_id, new_history)

        # 7. 准备响应数据
        response_data = {
            "status": "success",
            "session_id": session_id,
            "answer": answer,
            "context": [doc.get("text", "") for doc in reranked_results[:5]],
            "search_method": search_method,
            "answer_method": answer_method,
            "result_count": len(initial_results),
            "reranked_count": len(reranked_results),
            "model": getattr(config, 'LLM_MODEL_NAME', 'Qwen2.5-Coder-32B')
        }

        # 添加查询分析结果
        if query_analysis:
            response_data["query_analysis"] = {
                "category": query_analysis['classification']['category'],
                "confidence": query_analysis['classification']['confidence'],
                "entities": query_analysis['entities'],
                "complexity": query_analysis['complexity']
            }

        # 添加参考文档信息
        if reranked_results:
            references = []
            for doc in reranked_results[:5]:
                ref = {
                    "text": doc.get("text", "")[:300] + "..." if len(doc.get("text", "")) > 300 else doc.get("text", ""),
                    "score": doc.get("score", 0),
                    "method": doc.get("method", "unknown")
                }

                # 添加重排序分数作为准确率
                if "rerank_score" in doc:
                    ref["accuracy"] = min(int(doc["rerank_score"] * 100), 100)
                else:
                    ref["accuracy"] = min(int(doc.get("score", 0) * 100), 100)

                references.append(ref)

            response_data["references"] = references

        # 添加音频URL
        if audio_url:
            response_data["audio_url"] = audio_url

        log_info(f"查询处理完成: {session_id}", {
            "answer_length": len(answer),
            "references_count": len(references) if 'references' in response_data else 0
        })

        return jsonify(response_data)

    except Exception as e:
        log_error(e, {"context": "查询处理"})
        return jsonify({"status": "error", "message": "查询处理失败"})


@app.route('/speech-to-text', methods=['POST'])
def speech_to_text():
    """
    语音转文字API

    Returns:
        JSON响应，包含识别的文字
    """
    try:
        if not speech_processor:
            return jsonify({
                "status": "error",
                "message": "语音处理功能未启用"
            })

        # 检查音频文件
        if 'audio' not in request.files:
            return jsonify({
                "status": "error",
                "message": "没有音频文件"
            })

        audio_file = request.files['audio']
        if audio_file.filename == '':
            return jsonify({
                "status": "error",
                "message": "没有选择音频文件"
            })

        # 保存临时文件
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
            audio_file.save(temp_file.name)

            # 语音识别
            text = speech_processor.speech_to_text(temp_file.name)

            # 删除临时文件
            os.unlink(temp_file.name)

        if text:
            return jsonify({
                "status": "success",
                "text": text
            })
        else:
            return jsonify({
                "status": "error",
                "message": "语音识别失败"
            })

    except Exception as e:
        logger.error(f"语音转文字失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"语音转文字失败: {str(e)}"
        })


@app.route('/text-to-speech', methods=['POST'])
def text_to_speech():
    """
    文字转语音API

    Returns:
        音频文件或JSON响应
    """
    try:
        if not speech_processor:
            return jsonify({
                "status": "error",
                "message": "语音处理功能未启用"
            })

        data = request.json
        if not data or 'text' not in data:
            return jsonify({
                "status": "error",
                "message": "缺少文字内容"
            })

        text = data['text'].strip()
        if not text:
            return jsonify({
                "status": "error",
                "message": "文字内容不能为空"
            })

        # 生成音频文件
        import os
        import time

        audio_filename = f"tts_{int(time.time())}.wav"
        audio_path = os.path.join("static", "audio", audio_filename)

        # 确保音频目录存在
        os.makedirs(os.path.dirname(audio_path), exist_ok=True)

        # 生成语音
        result_path = speech_processor.text_to_speech(text, audio_path)

        if result_path:
            return jsonify({
                "status": "success",
                "audio_url": f"/static/audio/{audio_filename}"
            })
        else:
            return jsonify({
                "status": "error",
                "message": "语音合成失败"
            })

    except Exception as e:
        logger.error(f"文字转语音失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"文字转语音失败: {str(e)}"
        })


@app.route('/system-status', methods=['GET'])
@handle_exceptions(fallback_return=None)
@monitor_performance
def system_status():
    """
    获取系统状态信息

    Returns:
        JSON响应，包含系统各组件状态
    """
    try:
        # 获取系统监控状态
        monitor_status = get_system_status()

    # 组件状态检查
    components_status = {
        "milvus": collection is not None,
        "embedding_model": embedding_model is not None,
        "llm_client": llm_client is not None,
        "speech_processor": speech_processor is not None,
        "ner_classifier": ner_classifier is not None,
        "hybrid_search_engine": hybrid_search_engine is not None,
        "redis": redis_manager is not None
    }

    # 执行健康检查
    health_checks = {}
    try:
        health_checks["milvus"] = check_component_health("milvus", check_milvus_health)
    except:
        pass

    try:
        health_checks["redis"] = check_component_health("redis", check_redis_health)
    except:
        pass

    try:
        health_checks["llm"] = check_component_health("llm", check_llm_health)
    except:
        pass

    # 获取性能统计
    try:
        from error_handler import performance_monitor
        performance_stats = performance_monitor.get_performance_report()
    except ImportError:
        performance_stats = {"status": "not_available"}

    # 获取错误统计
    try:
        error_stats = error_handler.get_error_stats()
    except:
        error_stats = {"total_errors": 0}

    status_data = {
        "timestamp": time.time(),
        "app_info": {
            "name": getattr(config, 'APP_NAME', '智能文档问答系统'),
            "version": getattr(config, 'APP_VERSION', '2.0.0'),
            "environment": "development" if getattr(config, 'DEBUG', True) else "production"
        },
        "components": components_status,
        "health_checks": {name: status.__dict__ for name, status in health_checks.items()},
        "system_monitor": monitor_status,
        "performance": performance_stats,
        "errors": error_stats,
        "configuration": {
            "hybrid_search_enabled": getattr(config, 'HYBRID_SEARCH_ENABLED', False),
            "speech_enabled": SPEECH_ENABLED,
            "ner_enabled": NER_ENABLED,
            "max_file_size_mb": getattr(config, 'MAX_CONTENT_LENGTH', 50*1024*1024) // (1024 * 1024)
        }
    }

    return jsonify({
        "status": "success",
        "data": status_data
    })

    except Exception as e:
        log_error(e, {"context": "系统状态获取"})
        return jsonify({"status": "error", "message": "获取系统状态失败"})


@app.route('/clear-cache', methods=['POST'])
def clear_cache():
    """
    清空系统缓存

    Returns:
        JSON响应
    """
    try:
        cleared_caches = []

        # 清空检索缓存
        if hybrid_search_engine:
            hybrid_search_engine.clear_cache()
            cleared_caches.append("search_cache")

        # 可以添加其他缓存清理逻辑

        return jsonify({
            "status": "success",
            "message": f"已清空缓存: {', '.join(cleared_caches)}"
        })

    except Exception as e:
        logger.error(f"清空缓存失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"清空缓存失败: {str(e)}"
        })


@app.route('/chat-history/<session_id>', methods=['GET'])
def get_chat_history(session_id):
    """
    获取聊天历史

    Args:
        session_id: 会话ID

    Returns:
        JSON响应，包含聊天历史
    """
    try:
        history = redis_manager.get_session(session_id)
        return jsonify({
            "status": "success",
            "session_id": session_id,
            "history": history,
            "message_count": len(history)
        })

    except Exception as e:
        logger.error(f"获取聊天历史失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"获取聊天历史失败: {str(e)}"
        })


@app.route('/clear-history/<session_id>', methods=['POST'])
def clear_chat_history(session_id):
    """
    清空聊天历史

    Args:
        session_id: 会话ID

    Returns:
        JSON响应
    """
    try:
        redis_manager.store_session(session_id, [])
        return jsonify({
            "status": "success",
            "message": "聊天历史已清空"
        })

    except Exception as e:
        logger.error(f"清空聊天历史失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"清空聊天历史失败: {str(e)}"
        })


# 静态文件服务（用于音频文件）
@app.route('/static/audio/<filename>')
def serve_audio(filename):
    """
    提供音频文件服务

    Args:
        filename: 音频文件名

    Returns:
        音频文件响应
    """
    try:
        import os
        from flask import send_from_directory

        audio_dir = os.path.join("static", "audio")
        return send_from_directory(audio_dir, filename)

    except Exception as e:
        logger.error(f"音频文件服务失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": "音频文件不存在"
        }), 404


@app.errorhandler(404)
def not_found_error(error):
    """处理404错误"""
    return jsonify({
        "status": "error",
        "message": "请求的资源不存在",
        "error_code": 404
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """处理500错误"""
    log_error(error, {"request_path": request.path})
    return jsonify({
        "status": "error",
        "message": "服务器内部错误",
        "error_code": 500
    }), 500

@app.errorhandler(413)
def file_too_large(error):
    """处理文件过大错误"""
    max_size_mb = getattr(config, 'MAX_CONTENT_LENGTH', 50*1024*1024) // (1024*1024)
    return jsonify({
        "status": "error",
        "message": f"文件过大，最大支持 {max_size_mb}MB",
        "error_code": 413
    }), 413

@app.before_request
def before_request():
    """请求前处理"""
    # 记录请求信息
    if config.ENABLE_DETAILED_LOGGING:
        log_info(f"收到请求: {request.method} {request.path}", {
            "remote_addr": request.remote_addr,
            "user_agent": request.headers.get('User-Agent', '')[:100]
        })

@app.after_request
def after_request(response):
    """请求后处理"""
    # 添加CORS头
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')

    return response

def create_app():
    """
    应用工厂函数

    Returns:
        配置好的Flask应用
    """
    # 初始化应用组件
    if not initialize_application():
        log_error(Exception("应用初始化失败"))
        return None

    log_info("应用创建完成")
    return app

if __name__ == '__main__':
    try:
        # 创建应用
        application = create_app()
        if application is None:
            log_error(Exception("应用创建失败，退出"))
            exit(1)

        host = getattr(config, 'HOST', '0.0.0.0')
        port = getattr(config, 'PORT', 5000)
        debug = getattr(config, 'DEBUG', True)

        log_info(f"启动服务器: {host}:{port}")

        # 启动应用
        application.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )

    except KeyboardInterrupt:
        log_info("收到中断信号，正在关闭应用...")

        # 停止系统监控
        try:
            from system_monitor import stop_system_monitoring
            stop_system_monitoring()
        except ImportError:
            pass

        log_info("应用已关闭")

    except Exception as e:
        log_error(e, {"context": "应用启动"})
        exit(1)