from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection, utility
import logging
import json
from typing import List, Dict, Optional, Union

# 全局Milvus集合
collection = None


def init_milvus():
    global collection
    try:
        # 连接到Milvus
        connections.connect("default", host="localhost", port="19530")

        # 定义集合名称
        collection_name = "document_vectors"

        # 定义期望的字段schema
        expected_fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=1024),  # BGE-M3输出维度为1024
            # 新增元数据字段
            FieldSchema(name="chunk_type", dtype=DataType.VARCHAR, max_length=50),  # 文本块类型
            FieldSchema(name="has_table", dtype=DataType.BOOL),  # 是否包含表格
            FieldSchema(name="has_figure", dtype=DataType.BOOL),  # 是否包含图片
            FieldSchema(name="semantic_level", dtype=DataType.VARCHAR, max_length=20),  # 语义层级
            FieldSchema(name="chunk_length", dtype=DataType.INT64),  # 文本块长度
            FieldSchema(name="metadata_json", dtype=DataType.VARCHAR, max_length=2048)  # JSON格式的额外元数据
        ]

        # 检查集合是否已存在
        if utility.has_collection(collection_name):
            # 检查现有集合的schema是否匹配
            existing_collection = Collection(collection_name)
            existing_fields = [field.name for field in existing_collection.schema.fields]
            expected_field_names = [field.name for field in expected_fields]

            # 如果schema不匹配，删除旧集合
            if set(existing_fields) != set(expected_field_names):
                logging.warning(f"集合schema不匹配，删除旧集合并重新创建")
                logging.info(f"现有字段: {existing_fields}")
                logging.info(f"期望字段: {expected_field_names}")

                # 释放集合并删除
                existing_collection.release()
                utility.drop_collection(collection_name)
                logging.info("已删除旧集合")
            else:
                # Schema匹配，直接使用现有集合
                collection = existing_collection
                collection.load()
                logging.info("使用现有的兼容集合")
                return collection

        # 创建新集合（如果不存在或已删除旧集合）
        if not utility.has_collection(collection_name):
            # 创建集合 schema
            schema = CollectionSchema(expected_fields, "Enhanced document vectors with metadata for similarity search")

            # 创建集合
            collection = Collection(collection_name, schema)

            # 创建更高效的向量索引
            vector_index_params = {
                "index_type": "IVF_SQ8",  # 优化索引类型，平衡性能和存储
                "metric_type": "L2",
                "params": {"nlist": 2048}  # 增加聚类中心数量提高精度
            }
            collection.create_index("vector", vector_index_params)

            # 为元数据字段创建标量索引以提高过滤性能
            collection.create_index("chunk_type")
            collection.create_index("has_table")
            collection.create_index("has_figure")
            collection.create_index("semantic_level")

            logging.info("创建增强的集合schema和索引")
        else:
            collection = Collection(collection_name)

        # 加载集合
        collection.load()
        return collection
    except Exception as e:
        logging.error(f"Milvus initialization error: {str(e)}")
        raise


def store_vectors_with_metadata(chunks_with_metadata: List[Dict], embeddings, collection):
    """
    存储带元数据的向量

    Args:
        chunks_with_metadata: 带元数据的文本块列表
        embeddings: 嵌入向量
        collection: Milvus集合

    Returns:
        存储结果
    """
    try:
        if not chunks_with_metadata:
            return {"status": "error", "message": "没有数据需要存储"}

        # 验证集合schema
        schema_fields = [field.name for field in collection.schema.fields]
        expected_fields = ["id", "text", "vector", "chunk_type", "has_table", "has_figure", "semantic_level", "chunk_length", "metadata_json"]

        # 检查是否支持元数据字段
        if not all(field in schema_fields for field in expected_fields):
            logging.error(f"集合schema不支持元数据字段。现有字段: {schema_fields}")
            return {"status": "error", "message": "集合schema不支持元数据字段，请重新初始化集合"}

        # 准备数据
        texts = []
        vectors = []
        chunk_types = []
        has_tables = []
        has_figures = []
        semantic_levels = []
        chunk_lengths = []
        metadata_jsons = []

        for i, chunk_data in enumerate(chunks_with_metadata):
            text = chunk_data.get("text", "")
            metadata = chunk_data.get("metadata", {})

            texts.append(text)

            # 确保向量格式正确
            if i < len(embeddings):
                vector = embeddings[i]
                if hasattr(vector, 'tolist'):
                    vector = vector.tolist()
                vectors.append(vector)
            else:
                vectors.append([0.0] * 1024)

            # 提取元数据，确保类型正确
            chunk_types.append(str(metadata.get("type", "text")))
            has_tables.append(bool(metadata.get("has_table", False)))
            has_figures.append(bool(metadata.get("has_figure", False)))
            semantic_levels.append(str(metadata.get("semantic_level", "detail")))
            chunk_lengths.append(int(metadata.get("length", len(text))))

            # 序列化额外的元数据
            extra_metadata = {k: v for k, v in metadata.items()
                            if k not in ["type", "has_table", "has_figure", "semantic_level", "length"]}
            metadata_jsons.append(json.dumps(extra_metadata, ensure_ascii=False))

        # 验证数据长度一致性
        data_lengths = [len(texts), len(vectors), len(chunk_types), len(has_tables),
                       len(has_figures), len(semantic_levels), len(chunk_lengths), len(metadata_jsons)]
        if len(set(data_lengths)) > 1:
            logging.error(f"数据长度不一致: {data_lengths}")
            return {"status": "error", "message": "数据长度不一致"}

        # 构建插入数据 - 按照schema字段顺序排列
        data = [
            texts,           # text字段
            vectors,         # vector字段
            chunk_types,     # chunk_type字段
            has_tables,      # has_table字段
            has_figures,     # has_figure字段
            semantic_levels, # semantic_level字段
            chunk_lengths,   # chunk_length字段
            metadata_jsons   # metadata_json字段
        ]

        logging.info(f"准备插入 {len(texts)} 条记录，每条记录包含 {len(data)} 个字段")

        # 插入数据
        mr = collection.insert(data)
        collection.flush()

        logging.info(f"成功存储了 {len(texts)} 个带元数据的向量")
        return {"status": "success", "count": len(texts), "ids": mr.primary_keys}

    except Exception as e:
        logging.error(f"存储带元数据向量错误: {str(e)}")
        logging.error(f"错误类型: {type(e).__name__}")
        return {"status": "error", "message": str(e)}

def store_vectors(texts, embeddings, collection):
    """
    原始向量存储函数（向后兼容）

    Args:
        texts: 文本列表
        embeddings: 嵌入向量
        collection: Milvus集合

    Returns:
        存储结果
    """
    try:
        # 检查集合schema是否支持元数据
        schema_fields = [field.name for field in collection.schema.fields]
        logging.info(f"集合schema字段: {schema_fields}")

        if "chunk_type" in schema_fields:
            # 转换为带元数据格式
            logging.info("使用带元数据的存储格式")
            chunks_with_metadata = []
            for text in texts:
                chunks_with_metadata.append({
                    "text": text,
                    "metadata": {
                        "type": "text",
                        "has_table": False,
                        "has_figure": False,
                        "semantic_level": "detail",
                        "length": len(text)
                    }
                })

            return store_vectors_with_metadata(chunks_with_metadata, embeddings, collection)
        else:
            # 使用原始格式（只有text和vector字段）
            logging.info("使用原始存储格式")

            # 确保向量格式正确
            if hasattr(embeddings, 'tolist'):
                embeddings = embeddings.tolist()
            elif hasattr(embeddings[0], 'tolist'):
                embeddings = [emb.tolist() for emb in embeddings]

            data = [
                texts,      # text字段
                embeddings  # vector字段
            ]

            logging.info(f"准备插入 {len(texts)} 条记录，使用原始格式")

            # 插入数据
            mr = collection.insert(data)
            collection.flush()

            logging.info(f"成功存储了 {len(texts)} 个向量")
            return {"status": "success", "count": len(texts), "ids": mr.primary_keys}

    except Exception as e:
        logging.error(f"存储向量错误: {str(e)}")
        logging.error(f"错误类型: {type(e).__name__}")
        return {"status": "error", "message": str(e)}


def reset_collection(collection_name: str = "document_vectors"):
    """
    重置集合（删除并重新创建）

    Args:
        collection_name: 集合名称

    Returns:
        操作结果
    """
    try:
        # 检查集合是否存在
        if utility.has_collection(collection_name):
            # 获取集合并释放
            collection = Collection(collection_name)
            collection.release()

            # 删除集合
            utility.drop_collection(collection_name)
            logging.info(f"已删除集合: {collection_name}")

        # 重新创建集合
        new_collection = init_milvus()
        logging.info(f"已重新创建集合: {collection_name}")

        return {"status": "success", "message": "集合重置成功", "collection": new_collection}

    except Exception as e:
        logging.error(f"重置集合错误: {str(e)}")
        return {"status": "error", "message": str(e)}

def search_with_metadata_filter(collection, query_vector, filter_expr: Optional[str] = None,
                               top_k: int = 10, output_fields: Optional[List[str]] = None):
    """
    带元数据过滤的向量搜索

    Args:
        collection: Milvus集合
        query_vector: 查询向量
        filter_expr: 过滤表达式
        top_k: 返回结果数量
        output_fields: 输出字段列表

    Returns:
        搜索结果
    """
    try:
        # 默认输出字段
        if output_fields is None:
            schema_fields = [field.name for field in collection.schema.fields]
            output_fields = [field for field in schema_fields if field != "vector"]

        # 搜索参数
        search_params = {
            "metric_type": "L2",
            "params": {"nprobe": 16}
        }

        # 执行搜索
        results = collection.search(
            data=[query_vector],
            anns_field="vector",
            param=search_params,
            limit=top_k,
            expr=filter_expr,  # 添加过滤条件
            output_fields=output_fields
        )

        # 处理结果
        search_results = []
        for hits in results:
            for hit in hits:
                result = {
                    "id": hit.id,
                    "distance": hit.distance,
                    "score": 1.0 / (1.0 + hit.distance)
                }

                # 添加输出字段
                for field in output_fields:
                    result[field] = hit.entity.get(field)

                # 解析JSON元数据
                if "metadata_json" in result and result["metadata_json"]:
                    try:
                        extra_metadata = json.loads(result["metadata_json"])
                        result["extra_metadata"] = extra_metadata
                    except:
                        pass

                search_results.append(result)

        return search_results

    except Exception as e:
        logging.error(f"带过滤的搜索失败: {str(e)}")
        return []

def get_collection_stats(collection) -> Dict[str, Union[int, str]]:
    """
    获取集合统计信息

    Args:
        collection: Milvus集合

    Returns:
        统计信息字典
    """
    try:
        # 获取基本统计
        stats = {
            "name": collection.name,
            "description": collection.description,
            "num_entities": collection.num_entities,
            "is_empty": collection.is_empty
        }

        # 获取schema信息
        schema_info = {
            "fields": [{"name": field.name, "type": str(field.dtype)}
                      for field in collection.schema.fields],
            "field_count": len(collection.schema.fields)
        }
        stats["schema"] = schema_info

        # 获取索引信息
        indexes = collection.indexes
        index_info = []
        for index in indexes:
            index_info.append({
                "field_name": index.field_name,
                "index_type": index.params.get("index_type", "unknown"),
                "metric_type": index.params.get("metric_type", "unknown")
            })
        stats["indexes"] = index_info

        return stats

    except Exception as e:
        logging.error(f"获取集合统计失败: {str(e)}")
        return {"error": str(e)}