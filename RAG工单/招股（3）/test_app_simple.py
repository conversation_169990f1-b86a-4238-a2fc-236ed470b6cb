#!/usr/bin/env python3
"""
简化的应用程序测试版本
用于验证Milvus修复后的基本功能
"""

import sys
import os
import logging
from flask import Flask, request, jsonify, render_template
from flask_cors import CORS

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'test_secret_key'
CORS(app)

# 全局变量
collection = None
embedding_model = None

def initialize_components():
    """初始化核心组件"""
    global collection, embedding_model
    
    try:
        # 初始化Milvus
        from milvus_manager import init_milvus
        collection = init_milvus()
        logger.info("✅ Milvus初始化成功")
        
        # 初始化嵌入模型
        try:
            from embeddings import init_embedding_model
            embedding_model = init_embedding_model()
            logger.info("✅ 嵌入模型初始化成功")
        except Exception as e:
            logger.warning(f"⚠️ 嵌入模型初始化失败: {e}")
            embedding_model = None
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 组件初始化失败: {e}")
        return False

@app.route('/')
def index():
    """主页"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>招股（4）项目 - Milvus修复测试</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .status { padding: 20px; margin: 20px 0; border-radius: 5px; }
            .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
            .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
            .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
            .test-form { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
            input[type="text"] { width: 300px; padding: 8px; margin: 5px; }
            button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
            button:hover { background-color: #0056b3; }
        </style>
    </head>
    <body>
        <h1>🎉 招股（4）项目 - Milvus修复测试</h1>
        
        <div class="status success">
            <h3>✅ 修复完成！</h3>
            <p>Milvus schema不匹配问题已成功修复。系统现在可以正常处理向量存储和检索。</p>
        </div>
        
        <div class="info">
            <h3>📊 系统状态</h3>
            <p><strong>Milvus集合:</strong> """ + (collection.name if collection else "未初始化") + """</p>
            <p><strong>嵌入模型:</strong> """ + ("已加载" if embedding_model else "未加载") + """</p>
            <p><strong>Schema字段:</strong> """ + str([field.name for field in collection.schema.fields] if collection else []) + """</p>
        </div>
        
        <div class="test-form">
            <h3>🧪 测试功能</h3>
            <div>
                <h4>1. 测试向量存储</h4>
                <input type="text" id="testText" placeholder="输入测试文本" value="这是一个测试文档">
                <button onclick="testStorage()">测试存储</button>
                <div id="storageResult"></div>
            </div>
            
            <div style="margin-top: 20px;">
                <h4>2. 测试向量搜索</h4>
                <input type="text" id="searchText" placeholder="输入搜索查询" value="测试">
                <button onclick="testSearch()">测试搜索</button>
                <div id="searchResult"></div>
            </div>
        </div>
        
        <div class="info">
            <h3>🔗 相关链接</h3>
            <p><a href="/system-status">系统状态API</a></p>
            <p><a href="/test-milvus">Milvus测试API</a></p>
        </div>
        
        <script>
            function testStorage() {
                const text = document.getElementById('testText').value;
                fetch('/test-storage', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({text: text})
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('storageResult').innerHTML = 
                        '<div class="' + (data.status === 'success' ? 'success' : 'error') + '">' +
                        '<strong>存储结果:</strong> ' + JSON.stringify(data, null, 2) + '</div>';
                })
                .catch(error => {
                    document.getElementById('storageResult').innerHTML = 
                        '<div class="error"><strong>错误:</strong> ' + error + '</div>';
                });
            }
            
            function testSearch() {
                const text = document.getElementById('searchText').value;
                fetch('/test-search', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({query: text})
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('searchResult').innerHTML = 
                        '<div class="' + (data.status === 'success' ? 'success' : 'error') + '">' +
                        '<strong>搜索结果:</strong> ' + JSON.stringify(data, null, 2) + '</div>';
                })
                .catch(error => {
                    document.getElementById('searchResult').innerHTML = 
                        '<div class="error"><strong>错误:</strong> ' + error + '</div>';
                });
            }
        </script>
    </body>
    </html>
    """

@app.route('/system-status')
def system_status():
    """系统状态API"""
    try:
        status = {
            "milvus": {
                "connected": collection is not None,
                "collection_name": collection.name if collection else None,
                "schema_fields": [field.name for field in collection.schema.fields] if collection else []
            },
            "embedding_model": {
                "loaded": embedding_model is not None,
                "model_name": "BGE-M3" if embedding_model else None
            }
        }
        return jsonify({"status": "success", "data": status})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)})

@app.route('/test-storage', methods=['POST'])
def test_storage():
    """测试向量存储"""
    try:
        data = request.get_json()
        text = data.get('text', '测试文本')
        
        if not collection:
            return jsonify({"status": "error", "message": "Milvus未初始化"})
        
        # 创建测试数据
        chunks_with_metadata = [{
            "text": text,
            "metadata": {
                "type": "test",
                "has_table": False,
                "has_figure": False,
                "semantic_level": "detail",
                "length": len(text)
            }
        }]
        
        # 生成简单的测试向量
        import numpy as np
        test_embeddings = np.random.random((1, 1024)).astype(np.float32)
        
        # 存储向量
        from milvus_manager import store_vectors_with_metadata
        result = store_vectors_with_metadata(chunks_with_metadata, test_embeddings, collection)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"存储测试失败: {e}")
        return jsonify({"status": "error", "message": str(e)})

@app.route('/test-search', methods=['POST'])
def test_search():
    """测试向量搜索"""
    try:
        data = request.get_json()
        query = data.get('query', '测试')
        
        if not collection:
            return jsonify({"status": "error", "message": "Milvus未初始化"})
        
        # 生成查询向量
        import numpy as np
        query_vector = np.random.random(1024).astype(np.float32).tolist()
        
        # 搜索
        from milvus_manager import search_with_metadata_filter
        results = search_with_metadata_filter(
            collection=collection,
            query_vector=query_vector,
            top_k=5
        )
        
        return jsonify({
            "status": "success",
            "query": query,
            "result_count": len(results),
            "results": results[:3]  # 只返回前3个结果
        })
        
    except Exception as e:
        logger.error(f"搜索测试失败: {e}")
        return jsonify({"status": "error", "message": str(e)})

@app.route('/test-milvus')
def test_milvus():
    """Milvus测试页面"""
    try:
        if not collection:
            return jsonify({"status": "error", "message": "Milvus未初始化"})
        
        # 获取集合统计信息
        collection.load()
        stats = collection.num_entities
        
        return jsonify({
            "status": "success",
            "collection_name": collection.name,
            "entity_count": stats,
            "schema_fields": [field.name for field in collection.schema.fields]
        })
        
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)})

if __name__ == '__main__':
    logger.info("🚀 启动招股（4）项目测试应用...")
    
    # 初始化组件
    if initialize_components():
        logger.info("✅ 所有组件初始化成功")
    else:
        logger.warning("⚠️ 部分组件初始化失败，但应用仍可运行")
    
    # 启动应用
    logger.info("🌐 启动Web服务器: http://localhost:5001")
    app.run(host='0.0.0.0', port=5001, debug=True)
