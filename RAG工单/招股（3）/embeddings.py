# -*- coding: utf-8 -*-
"""
嵌入向量生成模块
使用BGE-M3模型将文本转换为高维向量表示
"""

# 导入必要的库
from sentence_transformers import SentenceTransformer  # 句子转换器库
import logging  # 日志记录
import numpy as np  # 数值计算
from typing import List, Union  # 类型提示
import os  # 操作系统接口

# 全局变量：嵌入模型实例
embedding_model = None

# 模型配置
MODEL_PATH = r"D:\model\BAAI\bge-m3"  # BGE-M3模型路径
MODEL_DIMENSION = 1024  # BGE-M3模型输出向量维度


def init_embedding_model():
    """
    初始化BGE-M3嵌入模型
    加载预训练的BGE-M3模型用于文本向量化

    Returns:
        SentenceTransformer: 初始化完成的嵌入模型

    Raises:
        Exception: 模型加载失败时抛出异常
    """
    global embedding_model  # 声明使用全局变量

    try:
        # 检查模型路径是否存在
        if not os.path.exists(MODEL_PATH):
            raise FileNotFoundError(f"BGE-M3模型路径不存在: {MODEL_PATH}")

        # 加载BGE-M3模型
        logging.info(f"正在加载BGE-M3嵌入模型: {MODEL_PATH}")
        embedding_model = SentenceTransformer(MODEL_PATH)

        # 验证模型加载成功
        test_embedding = embedding_model.encode(["测试文本"])  # 测试编码
        actual_dim = len(test_embedding[0])  # 获取实际向量维度

        # 验证向量维度
        if actual_dim != MODEL_DIMENSION:
            logging.warning(f"模型向量维度({actual_dim})与预期({MODEL_DIMENSION})不匹配")

        logging.info(f"BGE-M3模型加载成功，向量维度: {actual_dim}")
        return embedding_model

    except Exception as e:
        # 记录详细错误信息
        error_msg = f"BGE-M3嵌入模型初始化失败: {str(e)}"
        logging.error(error_msg)
        raise Exception(error_msg)


def generate_embeddings(texts: Union[str, List[str]]) -> np.ndarray:
    """
    生成文本的嵌入向量
    将输入文本转换为高维向量表示

    Args:
        texts: 单个文本字符串或文本列表

    Returns:
        np.ndarray: 嵌入向量数组，形状为(n_texts, dimension)

    Raises:
        Exception: 向量生成失败时抛出异常
    """
    global embedding_model  # 声明使用全局变量

    try:
        # 1. 检查模型是否已初始化
        if embedding_model is None:
            logging.info("嵌入模型未初始化，正在初始化...")
            init_embedding_model()  # 自动初始化模型

        # 2. 输入数据预处理
        # 确保输入是列表格式
        if isinstance(texts, str):
            texts = [texts]  # 单个字符串转换为列表

        # 验证输入数据
        if not texts or len(texts) == 0:
            raise ValueError("输入文本列表为空")

        # 过滤空文本
        valid_texts = [text for text in texts if text and text.strip()]
        if len(valid_texts) != len(texts):
            logging.warning(f"过滤了 {len(texts) - len(valid_texts)} 个空文本")

        if not valid_texts:
            raise ValueError("没有有效的文本需要编码")

        # 3. 生成嵌入向量
        logging.info(f"正在生成 {len(valid_texts)} 个文本的嵌入向量...")
        embeddings = embedding_model.encode(
            valid_texts,
            batch_size=32,          # 批处理大小，避免内存溢出
            show_progress_bar=True, # 显示进度条
            convert_to_numpy=True   # 转换为numpy数组
        )

        # 4. 验证输出结果
        if embeddings is None or len(embeddings) == 0:
            raise ValueError("嵌入向量生成失败，结果为空")

        # 确保输出是numpy数组
        if not isinstance(embeddings, np.ndarray):
            embeddings = np.array(embeddings)

        # 验证向量维度
        if len(embeddings.shape) != 2:
            raise ValueError(f"嵌入向量维度错误: {embeddings.shape}")

        actual_dim = embeddings.shape[1]  # 获取向量维度
        if actual_dim != MODEL_DIMENSION:
            logging.warning(f"生成的向量维度({actual_dim})与预期({MODEL_DIMENSION})不匹配")

        # 5. 记录成功信息
        logging.info(f"成功生成 {len(embeddings)} 个嵌入向量，维度: {actual_dim}")

        return embeddings

    except Exception as e:
        # 6. 错误处理
        error_msg = f"生成嵌入向量失败: {str(e)}"
        logging.error(error_msg)
        raise Exception(error_msg)


def get_model_info():
    """
    获取嵌入模型的详细信息

    Returns:
        dict: 模型信息字典
    """
    global embedding_model

    try:
        # 如果模型未初始化，先初始化
        if embedding_model is None:
            init_embedding_model()

        # 获取模型信息
        model_info = {
            "model_name": "BGE-M3",
            "model_path": MODEL_PATH,
            "dimension": MODEL_DIMENSION,
            "max_seq_length": getattr(embedding_model, 'max_seq_length', 'Unknown'),
            "model_loaded": embedding_model is not None
        }

        return model_info

    except Exception as e:
        logging.error(f"获取模型信息失败: {e}")
        return {"error": str(e)}