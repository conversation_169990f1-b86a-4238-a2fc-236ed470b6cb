import os
import logging
from typing import List, Dict, Optional
import numpy as np
from sentence_transformers import CrossEncoder

LOCAL_RERANK_MODEL_PATH = r"D:\model\BAAI\bge-reranker-v2-m3"
rerank_model = None

def rerank_documents(
    query: str,
    documents: List[Dict],
    top_n: int = 3,
    model_name: Optional[str] = None
) -> List[Dict]:
    global rerank_model

    if not documents:
        return []

    if rerank_model is None:
        try:
            logging.info("Loading local rerank model...")
            rerank_model = CrossEncoder(LOCAL_RERANK_MODEL_PATH)
            logging.info("Rerank model loaded.")
        except Exception as e:
            logging.error(f"Load rerank model failed: {e}")
            return documents[:top_n]

    try:
        pairs = [(query, doc["text"]) for doc in documents]
        scores = rerank_model.predict(pairs)

        for i, doc in enumerate(documents):
            rerank_score = float(scores[i])
            doc["rerank_score"] = rerank_score

            # 保留原始分数信息
            original_score = doc.get("score", 0)
            vector_score = doc.get("vector_score", original_score)
            keyword_score = doc.get("keyword_score", 0)
            semantic_boost = doc.get("semantic_boost", 0)

            # 综合评分：重排序分数(60%) + 原始混合分数(40%)
            final_score = rerank_score * 0.6 + original_score * 0.4
            doc["score"] = final_score

            # 保留详细分数信息供前端显示
            doc["score_details"] = {
                "rerank_score": rerank_score,
                "vector_score": vector_score,
                "keyword_score": keyword_score,
                "semantic_boost": semantic_boost,
                "final_score": final_score
            }

        # 按最终分数排序
        reranked = sorted(documents, key=lambda x: x["score"], reverse=True)

        # 记录重排序结果
        logging.info(f"Reranked {len(documents)} documents, returning top {top_n}")
        for i, doc in enumerate(reranked[:3]):  # 记录前3个结果的分数
            logging.info(f"Doc {i+1}: final={doc['score']:.3f}, rerank={doc['rerank_score']:.3f}")

        return reranked[:top_n]
    except Exception as e:
        logging.error(f"Rerank failed: {e}")
        return documents[:top_n]