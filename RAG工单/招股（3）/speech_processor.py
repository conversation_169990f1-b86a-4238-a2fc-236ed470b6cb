# -*- coding: utf-8 -*-
"""
语音处理模块
实现语音转文字(ASR)和文字转语音(TTS)功能
支持多种语音格式和高质量的语音合成
"""

import os
import io
import logging
import tempfile
import wave
from typing import Optional, Union, BinaryIO
import speech_recognition as sr
import pyttsx3
from pydub import AudioSegment
import threading
import queue
import time

# 配置日志
logger = logging.getLogger(__name__)

class SpeechProcessor:
    """
    语音处理器类
    提供语音识别(ASR)和语音合成(TTS)功能
    """
    
    def __init__(self):
        """
        初始化语音处理器
        设置语音识别器和TTS引擎
        """
        try:
            # 初始化语音识别器
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # 调整环境噪音
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                logger.info("语音识别器初始化成功，已调整环境噪音")
            
            # 初始化TTS引擎
            self.tts_engine = pyttsx3.init()
            self._configure_tts_engine()
            
            # 语音处理队列
            self.audio_queue = queue.Queue()
            self.is_processing = False
            
            logger.info("语音处理器初始化完成")
            
        except Exception as e:
            logger.error(f"语音处理器初始化失败: {str(e)}")
            raise
    
    def _configure_tts_engine(self):
        """
        配置TTS引擎参数
        设置语音速度、音量和语音类型
        """
        try:
            # 设置语音速度 (范围: 50-300, 默认200)
            self.tts_engine.setProperty('rate', 180)
            
            # 设置音量 (范围: 0.0-1.0)
            self.tts_engine.setProperty('volume', 0.8)
            
            # 获取可用的语音
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # 优先选择中文语音
                for voice in voices:
                    if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        logger.info(f"选择中文语音: {voice.name}")
                        break
                else:
                    # 如果没有中文语音，使用第一个可用语音
                    self.tts_engine.setProperty('voice', voices[0].id)
                    logger.info(f"使用默认语音: {voices[0].name}")
            
        except Exception as e:
            logger.warning(f"TTS引擎配置失败: {str(e)}")
    
    def speech_to_text(self, audio_data: Union[str, BinaryIO], 
                      language: str = "zh-CN") -> Optional[str]:
        """
        语音转文字
        
        Args:
            audio_data: 音频文件路径或音频数据流
            language: 识别语言，默认中文
            
        Returns:
            识别出的文字，失败返回None
        """
        try:
            # 处理不同类型的音频输入
            if isinstance(audio_data, str):
                # 文件路径
                audio_file = audio_data
            else:
                # 音频数据流，保存为临时文件
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                    temp_file.write(audio_data.read())
                    audio_file = temp_file.name
            
            # 转换音频格式为WAV (如果需要)
            audio_file = self._convert_to_wav(audio_file)
            
            # 使用语音识别
            with sr.AudioFile(audio_file) as source:
                # 读取音频数据
                audio = self.recognizer.record(source)
                
                # 尝试多种识别引擎
                text = self._recognize_with_fallback(audio, language)
                
                if text:
                    logger.info(f"语音识别成功: {text[:50]}...")
                    return text
                else:
                    logger.warning("语音识别失败")
                    return None
                    
        except Exception as e:
            logger.error(f"语音转文字失败: {str(e)}")
            return None
        finally:
            # 清理临时文件
            if isinstance(audio_data, (BinaryIO, io.BytesIO)) and 'audio_file' in locals():
                try:
                    os.unlink(audio_file)
                except:
                    pass
    
    def _convert_to_wav(self, audio_file: str) -> str:
        """
        将音频文件转换为WAV格式
        
        Args:
            audio_file: 原始音频文件路径
            
        Returns:
            WAV格式音频文件路径
        """
        try:
            # 检查是否已经是WAV格式
            if audio_file.lower().endswith('.wav'):
                return audio_file
            
            # 使用pydub转换格式
            audio = AudioSegment.from_file(audio_file)
            
            # 转换为WAV格式
            wav_file = audio_file.rsplit('.', 1)[0] + '.wav'
            audio.export(wav_file, format="wav")
            
            logger.info(f"音频格式转换完成: {audio_file} -> {wav_file}")
            return wav_file
            
        except Exception as e:
            logger.error(f"音频格式转换失败: {str(e)}")
            return audio_file
    
    def _recognize_with_fallback(self, audio, language: str) -> Optional[str]:
        """
        使用多种识别引擎进行语音识别
        
        Args:
            audio: 音频数据
            language: 识别语言
            
        Returns:
            识别结果文字
        """
        # 识别引擎优先级列表
        recognition_methods = [
            ("Google", lambda: self.recognizer.recognize_google(audio, language=language)),
            ("Google Cloud", lambda: self.recognizer.recognize_google_cloud(audio, language=language)),
            ("Sphinx", lambda: self.recognizer.recognize_sphinx(audio, language=language)),
        ]
        
        for engine_name, recognize_func in recognition_methods:
            try:
                result = recognize_func()
                logger.info(f"使用{engine_name}引擎识别成功")
                return result
            except sr.UnknownValueError:
                logger.warning(f"{engine_name}引擎无法理解音频")
                continue
            except sr.RequestError as e:
                logger.warning(f"{engine_name}引擎请求失败: {str(e)}")
                continue
            except Exception as e:
                logger.warning(f"{engine_name}引擎识别异常: {str(e)}")
                continue
        
        return None
    
    def text_to_speech(self, text: str, output_file: Optional[str] = None) -> Optional[str]:
        """
        文字转语音
        
        Args:
            text: 要转换的文字
            output_file: 输出音频文件路径，如果为None则直接播放
            
        Returns:
            输出文件路径或None
        """
        try:
            if not text or not text.strip():
                logger.warning("文字内容为空，无法转换语音")
                return None
            
            # 清理文本，移除特殊字符
            clean_text = self._clean_text_for_tts(text)
            
            if output_file:
                # 保存为音频文件
                self.tts_engine.save_to_file(clean_text, output_file)
                self.tts_engine.runAndWait()
                logger.info(f"语音合成完成，保存至: {output_file}")
                return output_file
            else:
                # 直接播放
                self._speak_async(clean_text)
                return None
                
        except Exception as e:
            logger.error(f"文字转语音失败: {str(e)}")
            return None
    
    def _clean_text_for_tts(self, text: str) -> str:
        """
        清理文本用于TTS
        移除Markdown格式和特殊字符
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        import re
        
        # 移除Markdown格式
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # 粗体
        text = re.sub(r'\*(.*?)\*', r'\1', text)      # 斜体
        text = re.sub(r'`(.*?)`', r'\1', text)        # 代码
        text = re.sub(r'#{1,6}\s*(.*)', r'\1', text)  # 标题
        text = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', text)  # 链接
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text).strip()
        
        # 限制文本长度 (TTS引擎可能有长度限制)
        if len(text) > 500:
            text = text[:500] + "..."
            
        return text
    
    def _speak_async(self, text: str):
        """
        异步播放语音
        
        Args:
            text: 要播放的文字
        """
        def speak():
            try:
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            except Exception as e:
                logger.error(f"语音播放失败: {str(e)}")
        
        # 在新线程中播放语音，避免阻塞主线程
        thread = threading.Thread(target=speak, daemon=True)
        thread.start()
    
    def record_audio(self, duration: int = 5, 
                    output_file: Optional[str] = None) -> Optional[str]:
        """
        录制音频
        
        Args:
            duration: 录制时长(秒)
            output_file: 输出文件路径
            
        Returns:
            录制的音频文件路径
        """
        try:
            logger.info(f"开始录制音频，时长: {duration}秒")
            
            with self.microphone as source:
                # 录制音频
                audio = self.recognizer.listen(source, timeout=duration, phrase_time_limit=duration)
            
            # 保存音频文件
            if not output_file:
                output_file = tempfile.mktemp(suffix='.wav')
            
            with open(output_file, 'wb') as f:
                f.write(audio.get_wav_data())
            
            logger.info(f"音频录制完成: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"音频录制失败: {str(e)}")
            return None
    
    def is_audio_file(self, filename: str) -> bool:
        """
        检查文件是否为音频文件
        
        Args:
            filename: 文件名
            
        Returns:
            是否为音频文件
        """
        audio_extensions = {'.wav', '.mp3', '.m4a', '.flac', '.aac', '.ogg', '.wma'}
        return any(filename.lower().endswith(ext) for ext in audio_extensions)
    
    def get_audio_info(self, audio_file: str) -> dict:
        """
        获取音频文件信息
        
        Args:
            audio_file: 音频文件路径
            
        Returns:
            音频信息字典
        """
        try:
            audio = AudioSegment.from_file(audio_file)
            return {
                'duration': len(audio) / 1000.0,  # 时长(秒)
                'channels': audio.channels,        # 声道数
                'sample_rate': audio.frame_rate,   # 采样率
                'format': audio_file.split('.')[-1].upper()  # 格式
            }
        except Exception as e:
            logger.error(f"获取音频信息失败: {str(e)}")
            return {}
    
    def cleanup(self):
        """
        清理资源
        """
        try:
            if hasattr(self, 'tts_engine'):
                self.tts_engine.stop()
            logger.info("语音处理器资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {str(e)}")

# 全局语音处理器实例
speech_processor = None

def init_speech_processor():
    """
    初始化全局语音处理器
    
    Returns:
        SpeechProcessor实例
    """
    global speech_processor
    try:
        if speech_processor is None:
            speech_processor = SpeechProcessor()
        return speech_processor
    except Exception as e:
        logger.error(f"语音处理器初始化失败: {str(e)}")
        return None

def get_speech_processor():
    """
    获取语音处理器实例
    
    Returns:
        SpeechProcessor实例或None
    """
    global speech_processor
    if speech_processor is None:
        return init_speech_processor()
    return speech_processor
