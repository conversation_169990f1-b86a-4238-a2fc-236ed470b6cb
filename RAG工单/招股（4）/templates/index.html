<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能文档问答系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        tertiary: '#8B5CF6',
                        neutral: '#64748B',
                        dark: '#1E293B',
                        light: '#F8FAFC',
                        card: '#F9FAFB',
                        accuracy: '#F59E0B' // 新增准确率颜色
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                    boxShadow: {
                        card: '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03)',
                        hover: '0 10px 15px -3px rgba(0, 0, 0, 0.07)'
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .shadow-soft {
                box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
            }
            .transition-custom {
                transition: all 0.3s ease;
            }
            .prose-custom {
                line-height: 1.7;
                max-width: none;
            }
            .prose-custom h3 {
                font-size: 1.25rem;
                font-weight: 600;
                margin-top: 1.5rem;
                margin-bottom: 1rem;
                color: #1E293B;
            }
            .prose-custom p {
                margin-bottom: 1rem;
            }
            .prose-custom ul {
                list-style-type: disc;
                padding-left: 1.5rem;
                margin-bottom: 1rem;
            }
            .prose-custom ol {
                list-style-type: decimal;
                padding-left: 1.5rem;
                margin-bottom: 1rem;
            }
            .prose-custom li {
                margin-bottom: 0.5rem;
            }
            .prose-custom blockquote {
                border-left: 4px solid #3B82F6;
                padding-left: 1rem;
                margin: 1.5rem 0;
                color: #64748B;
                font-style: italic;
            }
            .prose-custom strong {
                font-weight: 600;
            }
            /* 新增准确率进度条样式 */
            .accuracy-bar {
                height: 6px;
                border-radius: 3px;
                background-color: #E5E7EB;
                overflow: hidden;
            }
            .accuracy-fill {
                height: 100%;
                background-color: #F59E0B;
            }

            /* 聊天消息样式优化 */
            .chat-message {
                animation: fadeInUp 0.3s ease-out;
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .message-bubble {
                max-width: 85%;
                word-wrap: break-word;
                position: relative;
            }

            .message-bubble::before {
                content: '';
                position: absolute;
                width: 0;
                height: 0;
                border-style: solid;
            }

            .user-bubble::before {
                right: -8px;
                top: 12px;
                border-width: 8px 0 8px 8px;
                border-color: transparent transparent transparent #3B82F6;
            }

            .assistant-bubble::before {
                left: -8px;
                top: 12px;
                border-width: 8px 8px 8px 0;
                border-color: transparent #F3F4F6 transparent transparent;
            }

            .typing-indicator {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                background-color: #F3F4F6;
                border-radius: 18px;
                margin: 8px 0;
            }

            .typing-dots {
                display: flex;
                align-items: center;
                gap: 4px;
            }

            .typing-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: #9CA3AF;
                animation: typingDot 1.4s infinite ease-in-out;
            }

            .typing-dot:nth-child(1) { animation-delay: -0.32s; }
            .typing-dot:nth-child(2) { animation-delay: -0.16s; }

            @keyframes typingDot {
                0%, 80%, 100% {
                    transform: scale(0.8);
                    opacity: 0.5;
                }
                40% {
                    transform: scale(1);
                    opacity: 1;
                }
            }

            .message-actions {
                opacity: 0;
                transition: opacity 0.2s ease;
            }

            .chat-message:hover .message-actions {
                opacity: 1;
            }

            .quick-question-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen font-sans">
    <header class="bg-gradient-to-r from-primary to-tertiary shadow-lg sticky top-0 z-10">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <div class="bg-white p-2 rounded-full shadow-md">
                    <i class="fa fa-file-text-o text-primary text-xl"></i>
                </div>
                <h1 class="text-xl font-bold text-white">智能文档问答系统</h1>
            </div>
            <div class="text-xs text-white/80 bg-black/10 px-2 py-1 rounded">
                基于BGE-M3嵌入模型、BGE-reranker重排序和Milvus向量数据库
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- 文件上传区域 -->
        <section class="bg-white rounded-xl shadow-card p-6 mb-8 transition-custom hover:shadow-hover">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fa fa-cloud-upload text-primary mr-2"></i>
                上传文档
            </h2>
            <p class="text-neutral text-sm mb-4">支持上传PDF和TXT文件，系统将处理并存储文档内容以便后续查询</p>

            <div id="drop-area" class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center transition-custom hover:border-primary bg-card">
                <i class="fa fa-file-text-o text-4xl text-neutral mb-3"></i>
                <p class="mb-2 text-neutral-700">拖放文件到此处，或</p>
                <label class="inline-block bg-primary text-white px-4 py-2 rounded-lg cursor-pointer hover:bg-primary/90 transition-custom shadow-md">
                    <i class="fa fa-folder-open mr-1"></i> 选择文件
                    <input type="file" id="file-input" class="hidden" accept=".pdf,.txt">
                </label>
                <p class="text-xs text-neutral/60 mt-3">支持 PDF, TXT 格式</p>
            </div>

            <div id="upload-status" class="mt-4 hidden">
                <div class="flex items-center">
                    <i id="status-icon" class="mr-2"></i>
                    <span id="status-message"></span>
                </div>
                <div id="progress-container" class="mt-2 h-2 bg-gray-200 rounded-full overflow-hidden hidden">
                    <div id="progress-bar" class="h-full bg-secondary w-0 transition-all duration-300"></div>
                </div>
            </div>

        </section>

        <!-- 查询区域 -->
        <section class="bg-white rounded-xl shadow-card p-6 mb-8 transition-custom hover:shadow-hover">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fa fa-search text-secondary mr-2"></i>
                智能查询
            </h2>

            <!-- 查询选项 -->
            <div class="mb-4 flex flex-wrap gap-4">
                <label class="flex items-center">
                    <input type="checkbox" id="use-hybrid-search" checked class="mr-2">
                    <span class="text-sm">混合检索</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="use-ner-classification" checked class="mr-2">
                    <span class="text-sm">智能分类</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="enable-tts" class="mr-2">
                    <span class="text-sm">语音回答</span>
                </label>
            </div>

            <!-- 查询输入区域 -->
            <div class="relative mb-4">
                <input type="text" id="query-input" placeholder="输入您的问题，或点击语音按钮说话..." class="w-full px-4 py-3 pr-24 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent transition-custom">

                <!-- 语音输入按钮 -->
                <button id="voice-input-button" class="absolute right-16 top-1/2 transform -translate-y-1/2 bg-tertiary text-white px-3 py-1.5 rounded-md hover:bg-tertiary/90 transition-custom" title="语音输入">
                    <i class="fa fa-microphone"></i>
                </button>

                <!-- 查询按钮 -->
                <button id="search-button" class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary text-white px-4 py-1.5 rounded-md hover:bg-primary/90 transition-custom">
                    <i class="fa fa-paper-plane mr-1"></i> 查询
                </button>
            </div>

            <!-- 语音录制状态 -->
            <div id="voice-recording-status" class="mb-4 hidden">
                <div class="flex items-center justify-center p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse mr-3"></div>
                        <span class="text-red-700">正在录音，请说话...</span>
                        <button id="stop-recording" class="ml-4 bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                            停止录音
                        </button>
                    </div>
                </div>
            </div>

            <!-- 查询处理状态 -->
            <div id="query-status" class="mt-4 hidden flex items-center">
                <i class="fa fa-spinner fa-spin text-primary mr-2"></i>
                <span>正在处理您的查询...</span>
            </div>

            <!-- 优化的聊天对话区域 -->
            <div id="chat-container" class="mt-6 hidden">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold flex items-center">
                        <i class="fa fa-comments text-tertiary mr-2"></i>
                        智能对话
                        <span id="message-count" class="ml-2 text-sm bg-tertiary/20 text-tertiary px-2 py-1 rounded-full">0</span>
                    </h3>
                    <div class="flex items-center space-x-2">
                        <button id="export-chat-btn" class="text-sm text-blue-500 hover:text-blue-700 flex items-center">
                            <i class="fa fa-download mr-1"></i>
                            导出对话
                        </button>
                        <button id="clear-history-btn" class="text-sm text-red-500 hover:text-red-700 flex items-center">
                            <i class="fa fa-trash mr-1"></i>
                            清空历史
                        </button>
                    </div>
                </div>

                <!-- 聊天消息容器 -->
                <div id="chat-messages" class="bg-gray-50 rounded-xl p-4 space-y-4 max-h-[500px] overflow-y-auto border border-gray-200 shadow-inner">
                    <!-- 欢迎消息 -->
                    <div class="flex justify-center">
                        <div class="bg-white rounded-lg px-4 py-2 shadow-sm border border-gray-100">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fa fa-robot text-secondary mr-2"></i>
                                <span>您好！我是智能文档助手，请上传文档后开始提问。</span>
                            </div>
                        </div>
                    </div>
                    <!-- 动态聊天消息将在这里添加 -->
                </div>

                <!-- 快速提问建议 -->
                <div id="quick-questions" class="mt-4">
                    <div class="text-sm text-gray-600 mb-2">💡 快速提问：</div>
                    <div class="flex flex-wrap gap-2">
                        <button class="quick-question-btn bg-blue-50 hover:bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm transition-colors" data-question="这个文档的主要内容是什么？">
                            📄 文档概要
                        </button>
                        <button class="quick-question-btn bg-green-50 hover:bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm transition-colors" data-question="有哪些重要的数据和指标？">
                            📊 关键数据
                        </button>
                        <button class="quick-question-btn bg-purple-50 hover:bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm transition-colors" data-question="文档中提到了哪些风险？">
                            ⚠️ 风险分析
                        </button>
                        <button class="quick-question-btn bg-orange-50 hover:bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-sm transition-colors" data-question="有什么重要的时间节点？">
                            📅 时间节点
                        </button>
                    </div>
                </div>

                <!-- 对话统计信息 -->
                <div id="chat-stats" class="mt-4 hidden">
                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3 border border-blue-100">
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <div class="text-lg font-semibold text-blue-600" id="total-questions">0</div>
                                <div class="text-xs text-gray-600">总提问数</div>
                            </div>
                            <div>
                                <div class="text-lg font-semibold text-green-600" id="avg-response-time">0s</div>
                                <div class="text-xs text-gray-600">平均响应</div>
                            </div>
                            <div>
                                <div class="text-lg font-semibold text-purple-600" id="session-duration">0m</div>
                                <div class="text-xs text-gray-600">会话时长</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 答案展示区域 -->
            <div id="answer-container" class="mt-8 hidden">
                <div class="flex justify-between items-start mb-3">
                    <h3 class="text-lg font-semibold flex items-center">
                        <i class="fa fa-lightbulb-o text-yellow-500 mr-2"></i>
                        AI回答
                    </h3>
                    <div class="flex items-center space-x-2">
                        <!-- 语音播放按钮 -->
                        <button id="play-audio-btn" class="hidden text-sm bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 flex items-center">
                            <i class="fa fa-play mr-1"></i>
                            播放语音
                        </button>
                        <!-- 复制按钮 -->
                        <button id="copy-answer-btn" class="text-sm bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600 flex items-center">
                            <i class="fa fa-copy mr-1"></i>
                            复制
                        </button>
                    </div>
                </div>

                <!-- 查询分析信息 -->
                <div id="query-analysis-info" class="mb-4 hidden">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div class="flex items-center mb-2">
                            <i class="fa fa-brain text-blue-500 mr-2"></i>
                            <span class="font-medium text-blue-700">查询分析</span>
                        </div>
                        <div class="text-sm text-blue-600">
                            <span>类别: </span><span id="query-category" class="font-medium"></span>
                            <span class="ml-4">置信度: </span><span id="query-confidence" class="font-medium"></span>
                            <span class="ml-4">复杂度: </span><span id="query-complexity" class="font-medium"></span>
                        </div>
                        <div id="query-entities" class="mt-2 text-sm text-blue-600"></div>
                    </div>
                </div>

                <div class="bg-card rounded-xl p-4 prose-custom" id="answer-content"></div>

                <!-- 答案元信息 -->
                <div class="mt-3 text-sm text-neutral flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <span>推理模型: <span id="model-name" class="px-2 py-1 bg-secondary/20 text-secondary rounded-md">Qwen2.5-Coder-32B</span></span>
                        <span>检索方法: <span id="search-method" class="px-2 py-1 bg-primary/20 text-primary rounded-md">混合检索</span></span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span>结果数: <span id="result-count">0</span></span>
                        <span>会话ID: <span id="session-id" class="font-mono text-xs">-</span></span>
                    </div>
                </div>
            </div>

            <!-- 参考文档区域（增强可视化） -->
            <div id="reference-container" class="mt-6 hidden">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold flex items-center">
                        <i class="fa fa-file-text-o text-tertiary mr-2"></i>
                        参考文档
                        <span id="reference-count" class="ml-2 text-sm bg-tertiary/20 text-tertiary px-2 py-1 rounded-full">0</span>
                    </h3>
                    <div class="flex items-center space-x-2">
                        <button id="expand-all-refs" class="text-sm text-blue-500 hover:text-blue-700">
                            <i class="fa fa-expand mr-1"></i>展开全部
                        </button>
                        <button id="collapse-all-refs" class="text-sm text-blue-500 hover:text-blue-700">
                            <i class="fa fa-compress mr-1"></i>收起全部
                        </button>
                    </div>
                </div>

                <!-- 参考文档统计 -->
                <div id="reference-stats" class="mb-4 hidden">
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <div class="text-lg font-semibold text-primary" id="avg-accuracy">0%</div>
                                <div class="text-xs text-gray-600">平均相关度</div>
                            </div>
                            <div>
                                <div class="text-lg font-semibold text-secondary" id="vector-results">0</div>
                                <div class="text-xs text-gray-600">向量检索</div>
                            </div>
                            <div>
                                <div class="text-lg font-semibold text-tertiary" id="hybrid-results">0</div>
                                <div class="text-xs text-gray-600">混合检索</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-3" id="reference-list">
                    <!-- 动态添加参考文档 -->
                </div>
            </div>
        </section>

        <!-- 系统控制面板 -->
        <section class="bg-white rounded-xl shadow-card p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold flex items-center">
                    <i class="fa fa-cogs text-gray-600 mr-2"></i>
                    系统控制
                </h2>
                <button id="refresh-status" class="text-sm bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center">
                    <i class="fa fa-refresh mr-1"></i>
                    刷新状态
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- 系统状态指示器 -->
                <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium">系统状态</span>
                        <div id="system-status-indicator" class="w-3 h-3 bg-gray-400 rounded-full"></div>
                    </div>
                    <div id="system-status-text" class="text-xs text-gray-600 mt-1">检查中...</div>
                </div>

                <!-- 向量数据库状态 -->
                <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium">向量数据库</span>
                        <div id="milvus-status-indicator" class="w-3 h-3 bg-gray-400 rounded-full"></div>
                    </div>
                    <div id="milvus-status-text" class="text-xs text-gray-600 mt-1">文档数: -</div>
                </div>

                <!-- 语音功能状态 -->
                <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium">语音功能</span>
                        <div id="speech-status-indicator" class="w-3 h-3 bg-gray-400 rounded-full"></div>
                    </div>
                    <div id="speech-status-text" class="text-xs text-gray-600 mt-1">检查中...</div>
                </div>

                <!-- 缓存状态 -->
                <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium">缓存</span>
                        <button id="clear-cache-btn" class="text-xs text-red-500 hover:text-red-700">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                    <div id="cache-status-text" class="text-xs text-gray-600 mt-1">缓存数: -</div>
                </div>
            </div>
        </section>

        <!-- 系统信息卡片 -->
        <section class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div class="bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl p-4 border border-primary/20">
                <div class="flex items-center">
                    <div class="bg-primary/10 p-2 rounded-lg mr-3">
                        <i class="fa fa-microchip text-primary text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-primary">嵌入模型</h3>
                        <p class="text-sm">BGE-M3 · 本地推理</p>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-br from-secondary/10 to-secondary/5 rounded-xl p-4 border border-secondary/20">
                <div class="flex items-center">
                    <div class="bg-secondary/10 p-2 rounded-lg mr-3">
                        <i class="fa fa-database text-secondary text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-secondary">向量数据库</h3>
                        <p class="text-sm">Milvus · 1024维</p>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-br from-tertiary/10 to-tertiary/5 rounded-xl p-4 border border-tertiary/20">
                <div class="flex items-center">
                    <div class="bg-tertiary/10 p-2 rounded-lg mr-3">
                        <i class="fa fa-sort-amount-desc text-tertiary text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-tertiary">重排序模型</h3>
                        <p class="text-sm">BGE-reranker-v2-m3</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-dark text-white py-6">
        <div class="container mx-auto px-4 text-center text-sm">
            <p>智能文档问答系统 &copy; 2025 - 基于本地化AI技术构建</p>
            <p class="mt-2 text-white/70">本系统使用本地AI模型处理文档，确保数据安全</p>
        </div>
    </footer>

    <script>
        // 全局变量
        let currentSessionId = null;
        let isRecording = false;
        let mediaRecorder = null;
        let audioChunks = [];
        let messageCount = 0;
        let sessionStartTime = null;
        let totalQuestions = 0;
        let responseTimes = [];
        let isTyping = false;

        // DOM元素引用
        const dropArea = document.getElementById('drop-area');
        const fileInput = document.getElementById('file-input');
        const uploadStatus = document.getElementById('upload-status');
        const statusIcon = document.getElementById('status-icon');
        const statusMessage = document.getElementById('status-message');
        const progressContainer = document.getElementById('progress-container');
        const progressBar = document.getElementById('progress-bar');

        // 查询相关元素
        const queryInput = document.getElementById('query-input');
        const searchButton = document.getElementById('search-button');
        const voiceInputButton = document.getElementById('voice-input-button');
        const voiceRecordingStatus = document.getElementById('voice-recording-status');
        const stopRecordingButton = document.getElementById('stop-recording');

        // 选项元素
        const useHybridSearch = document.getElementById('use-hybrid-search');
        const useNerClassification = document.getElementById('use-ner-classification');
        const enableTts = document.getElementById('enable-tts');

        // 答案和历史相关元素
        const answerContainer = document.getElementById('answer-container');
        const answerContent = document.getElementById('answer-content');
        const chatContainer = document.getElementById('chat-container');
        const chatMessages = document.getElementById('chat-messages');
        const clearHistoryBtn = document.getElementById('clear-history-btn');
        const copyAnswerBtn = document.getElementById('copy-answer-btn');
        const playAudioBtn = document.getElementById('play-audio-btn');
        const exportChatBtn = document.getElementById('export-chat-btn');
        const messageCountEl = document.getElementById('message-count');
        const quickQuestions = document.querySelectorAll('.quick-question-btn');

        // 参考文档相关元素
        const referenceContainer = document.getElementById('reference-container');
        const referenceList = document.getElementById('reference-list');
        const referenceCount = document.getElementById('reference-count');
        const expandAllRefs = document.getElementById('expand-all-refs');
        const collapseAllRefs = document.getElementById('collapse-all-refs');

        // 系统状态相关元素
        const refreshStatusBtn = document.getElementById('refresh-status');
        const clearCacheBtn = document.getElementById('clear-cache-btn');

        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            // 绑定事件监听器
            bindEventListeners();

            // 检查系统状态
            checkSystemStatus();

            // 检查语音支持
            checkSpeechSupport();
        }

        function bindEventListeners() {
            // 语音输入相关
            voiceInputButton.addEventListener('click', toggleVoiceRecording);
            stopRecordingButton.addEventListener('click', stopVoiceRecording);

            // 查询相关
            searchButton.addEventListener('click', handleQuery);
            queryInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleQuery();
                }
            });

            // 历史记录相关
            clearHistoryBtn.addEventListener('click', clearChatHistory);
            copyAnswerBtn.addEventListener('click', copyAnswer);
            playAudioBtn.addEventListener('click', playAudio);
            exportChatBtn.addEventListener('click', exportChatHistory);

            // 快速提问按钮
            quickQuestions.forEach(btn => {
                btn.addEventListener('click', function() {
                    const question = this.dataset.question;
                    queryInput.value = question;
                    handleQuery();
                });
            });

            // 参考文档相关
            expandAllRefs.addEventListener('click', expandAllReferences);
            collapseAllRefs.addEventListener('click', collapseAllReferences);

            // 系统控制相关
            refreshStatusBtn.addEventListener('click', checkSystemStatus);
            clearCacheBtn.addEventListener('click', clearSystemCache);
        }

        // 语音录制功能
        async function toggleVoiceRecording() {
            if (isRecording) {
                stopVoiceRecording();
            } else {
                await startVoiceRecording();
            }
        }

        async function startVoiceRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];

                mediaRecorder.ondataavailable = function(event) {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = function() {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    sendAudioToServer(audioBlob);

                    // 停止所有音频轨道
                    stream.getTracks().forEach(track => track.stop());
                };

                mediaRecorder.start();
                isRecording = true;

                // 更新UI
                voiceRecordingStatus.classList.remove('hidden');
                voiceInputButton.innerHTML = '<i class="fa fa-stop"></i>';
                voiceInputButton.classList.add('bg-red-500');
                voiceInputButton.classList.remove('bg-tertiary');

            } catch (error) {
                console.error('语音录制启动失败:', error);
                showNotification('语音录制启动失败，请检查麦克风权限', 'error');
            }
        }

        function stopVoiceRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;

                // 更新UI
                voiceRecordingStatus.classList.add('hidden');
                voiceInputButton.innerHTML = '<i class="fa fa-microphone"></i>';
                voiceInputButton.classList.remove('bg-red-500');
                voiceInputButton.classList.add('bg-tertiary');
            }
        }

        async function sendAudioToServer(audioBlob) {
            try {
                const formData = new FormData();
                formData.append('audio', audioBlob, 'recording.wav');

                showNotification('正在识别语音...', 'info');

                const response = await fetch('/speech-to-text', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.status === 'success') {
                    queryInput.value = result.text;
                    showNotification('语音识别成功', 'success');
                } else {
                    showNotification('语音识别失败: ' + result.message, 'error');
                }

            } catch (error) {
                console.error('语音识别失败:', error);
                showNotification('语音识别失败', 'error');
            }
        }

        function checkSpeechSupport() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                voiceInputButton.disabled = true;
                voiceInputButton.title = '浏览器不支持语音录制';
                voiceInputButton.classList.add('opacity-50');
            }
        }

        // 增强的查询处理
        async function handleQuery() {
            const query = queryInput.value.trim();
            if (!query) {
                showNotification('请输入查询内容', 'warning');
                return;
            }

            // 记录查询开始时间
            const queryStartTime = Date.now();

            // 初始化会话
            if (!sessionStartTime) {
                sessionStartTime = Date.now();
            }

            // 添加用户消息到聊天
            addToChatHistory('user', query);

            // 显示AI正在思考
            showTypingIndicator();

            // 显示处理状态
            const queryStatus = document.getElementById('query-status');
            queryStatus.classList.remove('hidden');
            answerContainer.classList.add('hidden');
            referenceContainer.classList.add('hidden');

            // 禁用查询按钮和输入框
            searchButton.disabled = true;
            queryInput.disabled = true;
            searchButton.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i> 处理中...';

            // 清空输入框
            queryInput.value = '';

            try {
                const requestData = {
                    query: query,
                    use_hybrid_search: useHybridSearch.checked,
                    use_ner_classification: useNerClassification.checked,
                    enable_tts: enableTts.checked
                };

                const response = await fetch('/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();

                // 隐藏处理状态和打字指示器
                queryStatus.classList.add('hidden');
                hideTypingIndicator();

                if (data.status === 'success') {
                    // 计算响应时间
                    const responseTime = (Date.now() - queryStartTime) / 1000;
                    responseTimes.push(responseTime);
                    totalQuestions++;

                    // 更新会话ID
                    currentSessionId = data.session_id;
                    document.getElementById('session-id').textContent = currentSessionId.substring(0, 8);

                    // 添加AI回答到聊天
                    addToChatHistory('assistant', data.answer, {
                        model: data.model,
                        searchMethod: data.search_method,
                        resultCount: data.result_count,
                        responseTime: responseTime,
                        audioUrl: data.audio_url
                    });

                    // 显示答案（保持原有功能）
                    displayAnswer(data);

                    // 显示参考文档
                    if (data.references && data.references.length > 0) {
                        displayReferences(data.references);
                    }

                    // 显示查询分析
                    if (data.query_analysis) {
                        displayQueryAnalysis(data.query_analysis);
                    }

                    // 处理语音回答
                    if (data.audio_url) {
                        setupAudioPlayback(data.audio_url);
                    }

                    // 更新统计信息
                    updateChatStats();
                    updateSearchStats(data);

                } else {
                    showNotification('查询失败: ' + data.message, 'error');
                }

            } catch (error) {
                console.error('查询失败:', error);
                showNotification('查询失败，请稍后再试', 'error');
                queryStatus.classList.add('hidden');
                hideTypingIndicator();

                // 添加错误消息到聊天
                addToChatHistory('assistant', '抱歉，处理您的请求时出现了错误，请稍后再试。', {
                    isError: true
                });
            } finally {
                // 恢复查询按钮和输入框
                searchButton.disabled = false;
                queryInput.disabled = false;
                searchButton.innerHTML = '<i class="fa fa-paper-plane mr-1"></i> 查询';

                // 聚焦输入框
                queryInput.focus();
            }
        }

        function displayAnswer(data) {
            answerContent.innerHTML = data.answer;

            // 更新模型信息
            document.getElementById('model-name').textContent = data.model || 'Qwen2.5-Coder-32B';
            document.getElementById('search-method').textContent = data.search_method || '向量检索';
            document.getElementById('result-count').textContent = data.result_count || 0;

            answerContainer.classList.remove('hidden');
        }

        function displayQueryAnalysis(analysis) {
            const queryAnalysisInfo = document.getElementById('query-analysis-info');
            const queryCategory = document.getElementById('query-category');
            const queryConfidence = document.getElementById('query-confidence');
            const queryComplexity = document.getElementById('query-complexity');
            const queryEntities = document.getElementById('query-entities');

            queryCategory.textContent = analysis.category || '未分类';
            queryConfidence.textContent = Math.round((analysis.confidence || 0) * 100) + '%';
            queryComplexity.textContent = analysis.complexity || '简单';

            // 显示实体信息
            if (analysis.entities && Object.keys(analysis.entities).length > 0) {
                const entityTexts = [];
                for (const [type, entities] of Object.entries(analysis.entities)) {
                    if (entities.length > 0) {
                        const entityNames = entities.map(e => e.text).join(', ');
                        entityTexts.push(`${type}: ${entityNames}`);
                    }
                }
                queryEntities.textContent = '实体: ' + entityTexts.join(' | ');
            } else {
                queryEntities.textContent = '未识别到实体';
            }

            queryAnalysisInfo.classList.remove('hidden');
        }

        function displayReferences(references) {
            referenceList.innerHTML = '';
            referenceCount.textContent = references.length;

            // 计算统计信息
            let totalAccuracy = 0;
            let vectorCount = 0;
            let hybridCount = 0;

            references.forEach((ref, index) => {
                totalAccuracy += ref.accuracy || 0;
                if (ref.method === 'vector') vectorCount++;
                if (ref.method === 'hybrid') hybridCount++;

                const referenceElement = createReferenceElement(ref, index);
                referenceList.appendChild(referenceElement);
            });

            // 更新统计信息
            document.getElementById('avg-accuracy').textContent = Math.round(totalAccuracy / references.length) + '%';
            document.getElementById('vector-results').textContent = vectorCount;
            document.getElementById('hybrid-results').textContent = hybridCount;

            document.getElementById('reference-stats').classList.remove('hidden');
            referenceContainer.classList.remove('hidden');
        }

        function createReferenceElement(ref, index) {
            const div = document.createElement('div');
            div.className = 'bg-card rounded-lg border border-gray-100 overflow-hidden reference-item';

            const accuracy = ref.accuracy || 0;
            const methodColor = {
                'vector': 'bg-blue-100 text-blue-800',
                'keyword': 'bg-green-100 text-green-800',
                'semantic': 'bg-purple-100 text-purple-800',
                'hybrid': 'bg-orange-100 text-orange-800'
            }[ref.method] || 'bg-gray-100 text-gray-800';

            div.innerHTML = `
                <div class="p-4">
                    <div class="flex justify-between items-start mb-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-xs font-medium px-2 py-1 rounded-full ${methodColor}">
                                ${ref.method || 'unknown'}
                            </span>
                            <span class="text-xs font-medium px-2 py-1 rounded-full bg-accuracy/20 text-accuracy">
                                相关度: ${accuracy}%
                            </span>
                        </div>
                        <button class="toggle-ref-btn text-sm text-blue-500 hover:text-blue-700" data-index="${index}">
                            <i class="fa fa-chevron-down"></i>
                        </button>
                    </div>

                    <div class="accuracy-bar mb-3">
                        <div class="accuracy-fill" style="width: ${accuracy}%"></div>
                    </div>

                    <div class="reference-content">
                        <p class="text-sm text-gray-700 line-clamp-3">${ref.text}</p>
                        <div class="reference-full-content hidden mt-2">
                            <p class="text-sm text-gray-700 whitespace-pre-wrap">${ref.text}</p>
                        </div>
                    </div>
                </div>
            `;

            // 绑定展开/收起事件
            const toggleBtn = div.querySelector('.toggle-ref-btn');
            const content = div.querySelector('.reference-content p');
            const fullContent = div.querySelector('.reference-full-content');

            toggleBtn.addEventListener('click', function() {
                const isExpanded = !fullContent.classList.contains('hidden');

                if (isExpanded) {
                    fullContent.classList.add('hidden');
                    content.classList.remove('hidden');
                    toggleBtn.innerHTML = '<i class="fa fa-chevron-down"></i>';
                } else {
                    fullContent.classList.remove('hidden');
                    content.classList.add('hidden');
                    toggleBtn.innerHTML = '<i class="fa fa-chevron-up"></i>';
                }
            });

            return div;
        }

        function addToChatHistory(role, content, metadata = {}) {
            // 移除欢迎消息（如果存在）
            const welcomeMsg = chatMessages.querySelector('.flex.justify-center');
            if (welcomeMsg && messageCount === 0) {
                welcomeMsg.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message flex ${role === 'user' ? 'justify-end' : 'justify-start'} mb-4`;

            const bubbleClass = role === 'user'
                ? 'bg-primary text-white user-bubble'
                : metadata.isError
                    ? 'bg-red-50 text-red-800 border border-red-200 assistant-bubble'
                    : 'bg-white text-gray-800 border border-gray-200 shadow-sm assistant-bubble';

            const icon = role === 'user' ? 'fa-user' : 'fa-robot';
            const timestamp = new Date().toLocaleTimeString();

            let messageContent = `
                <div class="flex items-start space-x-3 max-w-4xl">
                    ${role === 'assistant' ? `
                        <div class="w-8 h-8 rounded-full ${metadata.isError ? 'bg-red-500' : 'bg-secondary'} flex items-center justify-center flex-shrink-0 mt-1">
                            <i class="fa ${metadata.isError ? 'fa-exclamation-triangle' : icon} text-white text-sm"></i>
                        </div>
                    ` : ''}

                    <div class="message-bubble ${bubbleClass} rounded-2xl px-4 py-3 relative">
                        <div class="text-sm whitespace-pre-wrap leading-relaxed">${content}</div>

                        <div class="flex items-center justify-between mt-2 pt-2 border-t border-gray-100 text-xs opacity-70">
                            <span>${timestamp}</span>
                            ${role === 'assistant' && !metadata.isError ? `
                                <div class="message-actions flex items-center space-x-2">
                                    ${metadata.responseTime ? `<span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-full">${metadata.responseTime.toFixed(1)}s</span>` : ''}
                                    ${metadata.model ? `<span class="bg-green-100 text-green-600 px-2 py-1 rounded-full">${metadata.model}</span>` : ''}
                                    <button class="copy-message-btn text-gray-500 hover:text-gray-700 p-1" title="复制消息">
                                        <i class="fa fa-copy"></i>
                                    </button>
                                    ${metadata.audioUrl ? `
                                        <button class="play-message-audio-btn text-blue-500 hover:text-blue-700 p-1" data-audio="${metadata.audioUrl}" title="播放语音">
                                            <i class="fa fa-volume-up"></i>
                                        </button>
                                    ` : ''}
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    ${role === 'user' ? `
                        <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center flex-shrink-0 mt-1">
                            <i class="fa ${icon} text-white text-sm"></i>
                        </div>
                    ` : ''}
                </div>
            `;

            messageDiv.innerHTML = messageContent;

            // 绑定消息操作事件
            const copyBtn = messageDiv.querySelector('.copy-message-btn');
            if (copyBtn) {
                copyBtn.addEventListener('click', () => {
                    navigator.clipboard.writeText(content).then(() => {
                        showNotification('消息已复制', 'success');
                    });
                });
            }

            const audioBtn = messageDiv.querySelector('.play-message-audio-btn');
            if (audioBtn) {
                audioBtn.addEventListener('click', () => {
                    const audioUrl = audioBtn.dataset.audio;
                    const audio = new Audio(audioUrl);
                    audio.play().catch(error => {
                        showNotification('音频播放失败', 'error');
                    });
                });
            }

            chatMessages.appendChild(messageDiv);
            chatContainer.classList.remove('hidden');

            // 更新消息计数
            messageCount++;
            messageCountEl.textContent = messageCount;

            // 滚动到底部（平滑滚动）
            chatMessages.scrollTo({
                top: chatMessages.scrollHeight,
                behavior: 'smooth'
            });

            // 显示统计信息
            if (messageCount >= 2) {
                document.getElementById('chat-stats').classList.remove('hidden');
            }
        }

        function setupAudioPlayback(audioUrl) {
            playAudioBtn.classList.remove('hidden');
            playAudioBtn.onclick = function() {
                const audio = new Audio(audioUrl);
                audio.play().catch(error => {
                    console.error('音频播放失败:', error);
                    showNotification('音频播放失败', 'error');
                });
            };
        }

        // 显示AI正在思考的指示器
        function showTypingIndicator() {
            if (isTyping) return;

            isTyping = true;
            const typingDiv = document.createElement('div');
            typingDiv.id = 'typing-indicator';
            typingDiv.className = 'chat-message flex justify-start mb-4';

            typingDiv.innerHTML = `
                <div class="flex items-start space-x-3 max-w-4xl">
                    <div class="w-8 h-8 rounded-full bg-secondary flex items-center justify-center flex-shrink-0 mt-1">
                        <i class="fa fa-robot text-white text-sm"></i>
                    </div>
                    <div class="typing-indicator">
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                        <span class="ml-2 text-sm text-gray-600">AI正在思考...</span>
                    </div>
                </div>
            `;

            chatMessages.appendChild(typingDiv);
            chatMessages.scrollTo({
                top: chatMessages.scrollHeight,
                behavior: 'smooth'
            });
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
                isTyping = false;
            }
        }

        // 更新聊天统计信息
        function updateChatStats() {
            document.getElementById('total-questions').textContent = totalQuestions;

            if (responseTimes.length > 0) {
                const avgTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
                document.getElementById('avg-response-time').textContent = avgTime.toFixed(1) + 's';
            }

            if (sessionStartTime) {
                const duration = Math.floor((Date.now() - sessionStartTime) / 60000);
                document.getElementById('session-duration').textContent = duration + 'm';
            }
        }

        // 导出聊天记录
        function exportChatHistory() {
            if (messageCount === 0) {
                showNotification('没有聊天记录可导出', 'warning');
                return;
            }

            const messages = Array.from(chatMessages.querySelectorAll('.chat-message')).map(msg => {
                const isUser = msg.classList.contains('justify-end');
                const content = msg.querySelector('.text-sm').textContent;
                const timestamp = msg.querySelector('.text-xs').textContent;

                return {
                    role: isUser ? 'user' : 'assistant',
                    content: content,
                    timestamp: timestamp
                };
            });

            const exportData = {
                sessionId: currentSessionId,
                exportTime: new Date().toISOString(),
                messageCount: messageCount,
                totalQuestions: totalQuestions,
                sessionDuration: sessionStartTime ? Math.floor((Date.now() - sessionStartTime) / 60000) : 0,
                messages: messages
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `chat-history-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification('聊天记录已导出', 'success');
        }

        function updateSearchStats(data) {
            // 可以在这里更新更多统计信息
            console.log('搜索统计:', data);
        }

        // 聊天历史管理
        async function clearChatHistory() {
            if (messageCount === 0) {
                showNotification('没有聊天记录', 'warning');
                return;
            }

            if (!confirm('确定要清空聊天历史吗？这将删除所有对话记录。')) {
                return;
            }

            try {
                if (currentSessionId) {
                    const response = await fetch(`/clear-history/${currentSessionId}`, {
                        method: 'POST'
                    });

                    const result = await response.json();
                    if (result.status !== 'success') {
                        showNotification('服务器清空失败: ' + result.message, 'error');
                        return;
                    }
                }

                // 重置聊天界面
                chatMessages.innerHTML = `
                    <div class="flex justify-center">
                        <div class="bg-white rounded-lg px-4 py-2 shadow-sm border border-gray-100">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fa fa-robot text-secondary mr-2"></i>
                                <span>您好！我是智能文档助手，请上传文档后开始提问。</span>
                            </div>
                        </div>
                    </div>
                `;

                // 重置统计数据
                messageCount = 0;
                totalQuestions = 0;
                responseTimes = [];
                sessionStartTime = null;
                currentSessionId = null;

                // 更新UI
                messageCountEl.textContent = '0';
                document.getElementById('chat-stats').classList.add('hidden');
                document.getElementById('session-id').textContent = '-';

                showNotification('聊天历史已清空', 'success');

            } catch (error) {
                console.error('清空历史失败:', error);
                showNotification('清空历史失败', 'error');
            }
        }

        function copyAnswer() {
            const answerText = answerContent.textContent;
            navigator.clipboard.writeText(answerText).then(() => {
                showNotification('答案已复制到剪贴板', 'success');
            }).catch(() => {
                showNotification('复制失败', 'error');
            });
        }

        function playAudio() {
            // 这个函数在setupAudioPlayback中动态设置
        }

        function expandAllReferences() {
            document.querySelectorAll('.reference-item .toggle-ref-btn').forEach(btn => {
                const fullContent = btn.closest('.reference-item').querySelector('.reference-full-content');
                const content = btn.closest('.reference-item').querySelector('.reference-content p');

                if (fullContent.classList.contains('hidden')) {
                    btn.click();
                }
            });
        }

        function collapseAllReferences() {
            document.querySelectorAll('.reference-item .toggle-ref-btn').forEach(btn => {
                const fullContent = btn.closest('.reference-item').querySelector('.reference-full-content');

                if (!fullContent.classList.contains('hidden')) {
                    btn.click();
                }
            });
        }

        // 系统状态检查
        async function checkSystemStatus() {
            try {
                const response = await fetch('/system-status');
                const result = await response.json();

                if (result.status === 'success') {
                    updateSystemStatusUI(result.data);
                } else {
                    showNotification('获取系统状态失败', 'error');
                }

            } catch (error) {
                console.error('系统状态检查失败:', error);
                showNotification('系统状态检查失败', 'error');
            }
        }

        function updateSystemStatusUI(statusData) {
            const components = statusData.components;
            const statistics = statusData.statistics;

            // 更新系统状态指示器
            const systemStatusIndicator = document.getElementById('system-status-indicator');
            const systemStatusText = document.getElementById('system-status-text');

            const allComponentsOk = Object.values(components).every(status => status);

            if (allComponentsOk) {
                systemStatusIndicator.className = 'w-3 h-3 bg-green-500 rounded-full';
                systemStatusText.textContent = '系统正常';
            } else {
                systemStatusIndicator.className = 'w-3 h-3 bg-red-500 rounded-full';
                systemStatusText.textContent = '部分功能异常';
            }

            // 更新Milvus状态
            const milvusStatusIndicator = document.getElementById('milvus-status-indicator');
            const milvusStatusText = document.getElementById('milvus-status-text');

            if (components.milvus) {
                milvusStatusIndicator.className = 'w-3 h-3 bg-green-500 rounded-full';
                const docCount = statistics.milvus?.num_entities || 0;
                milvusStatusText.textContent = `文档数: ${docCount}`;
            } else {
                milvusStatusIndicator.className = 'w-3 h-3 bg-red-500 rounded-full';
                milvusStatusText.textContent = '连接失败';
            }

            // 更新语音功能状态
            const speechStatusIndicator = document.getElementById('speech-status-indicator');
            const speechStatusText = document.getElementById('speech-status-text');

            if (components.speech_processor) {
                speechStatusIndicator.className = 'w-3 h-3 bg-green-500 rounded-full';
                speechStatusText.textContent = '功能正常';
            } else {
                speechStatusIndicator.className = 'w-3 h-3 bg-yellow-500 rounded-full';
                speechStatusText.textContent = '功能未启用';
            }

            // 更新缓存状态
            const cacheStatusText = document.getElementById('cache-status-text');
            const cacheCount = statistics.search_cache?.cache_size || 0;
            cacheStatusText.textContent = `缓存数: ${cacheCount}`;
        }

        async function clearSystemCache() {
            if (!confirm('确定要清空系统缓存吗？')) {
                return;
            }

            try {
                const response = await fetch('/clear-cache', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.status === 'success') {
                    showNotification('缓存已清空', 'success');
                    checkSystemStatus(); // 刷新状态
                } else {
                    showNotification('清空缓存失败: ' + result.message, 'error');
                }

            } catch (error) {
                console.error('清空缓存失败:', error);
                showNotification('清空缓存失败', 'error');
            }
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'warning': 'bg-yellow-500',
                'info': 'bg-blue-500'
            }[type] || 'bg-blue-500';

            const icon = {
                'success': 'fa-check-circle',
                'error': 'fa-times-circle',
                'warning': 'fa-exclamation-triangle',
                'info': 'fa-info-circle'
            }[type] || 'fa-info-circle';

            notification.className += ` ${bgColor} text-white`;

            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fa ${icon} mr-2"></i>
                    <span class="flex-1">${message}</span>
                    <button class="ml-2 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropArea.classList.add('border-primary', 'bg-blue-50');
        }

        function unhighlight() {
            dropArea.classList.remove('border-primary', 'bg-blue-50');
        }

        dropArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        fileInput.addEventListener('change', function() {
            handleFiles(this.files);
        });

        function handleFiles(files) {
            if (files.length === 0) return;

            const file = files[0];
            if (!allowedFile(file.name, ['pdf', 'txt'])) {
                showStatus('error', '仅支持 PDF 和 TXT 文件');
                return;
            }

            showStatus('processing', '正在上传文件...');

            // 模拟上传进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += 5;
                progressBar.style.width = `${progress}%`;
                if (progress >= 100) {
                    clearInterval(interval);
                    uploadFile(file);
                }
            }, 100);
        }

        function allowedFile(filename, allowedExtensions) {
            const extension = filename.split('.').pop().toLowerCase();
            return allowedExtensions.includes(extension);
        }

        function showStatus(type, message) {
            uploadStatus.classList.remove('hidden');
            progressContainer.classList.remove('hidden');

            if (type === 'success') {
                statusIcon.className = 'fa fa-check-circle text-green-500 mr-2';
            } else if (type === 'error') {
                statusIcon.className = 'fa fa-times-circle text-red-500 mr-2';
                progressContainer.classList.add('hidden');
            } else {
                statusIcon.className = 'fa fa-spinner fa-spin text-primary mr-2';
            }

            statusMessage.textContent = message;
        }

        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showStatus('success', '文件上传并处理成功！');
                } else {
                    showStatus('error', `上传失败: ${data.message}`);
                }
            })
            .catch(error => {
                showStatus('error', '上传过程中出错');
                console.error('上传错误:', error);
            });
        }

        // 文件上传处理（更新版本）
        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showStatus('success', data.message);

                    // 显示详细信息
                    if (data.stats) {
                        const statsText = `文件类型: ${data.file_type}, 总块数: ${data.stats.total_chunks}, 文本块: ${data.stats.text_chunks}, 表格块: ${data.stats.table_chunks}`;
                        showNotification(statsText, 'info');
                    }

                    // 刷新系统状态
                    setTimeout(checkSystemStatus, 1000);
                } else {
                    showStatus('error', `上传失败: ${data.message}`);
                }
            })
            .catch(error => {
                showStatus('error', '上传过程中出错');
                console.error('上传错误:', error);
            });
        }
    </script>
</body>
</html>