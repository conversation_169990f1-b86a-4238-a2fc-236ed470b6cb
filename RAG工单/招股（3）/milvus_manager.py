# -*- coding: utf-8 -*-
"""
Milvus向量数据库管理模块
负责向量数据库的连接、集合管理、索引创建和数据存储
"""

# 导入Milvus相关库
from pymilvus import (
    connections,      # 数据库连接管理
    FieldSchema,      # 字段模式定义
    CollectionSchema, # 集合模式定义
    DataType,         # 数据类型枚举
    Collection,       # 集合操作类
    utility          # 工具函数
)
import logging  # 日志记录
import numpy as np  # 数值计算

# 全局配置常量
COLLECTION_NAME = "document_vectors"  # 集合名称
DIM = 1024  # BGE-M3嵌入向量维度
MILVUS_HOST, MILVUS_PORT = "localhost", 19530  # Milvus服务器地址和端口


def init_milvus() -> Collection:
    """
    初始化Milvus向量数据库连接和集合
    创建优化的HNSW索引以提高检索性能

    Returns:
        Collection: 初始化完成的Milvus集合对象
    """
    # 1. 连接到Milvus服务器
    connections.connect("default", host=MILVUS_HOST, port=MILVUS_PORT)
    logging.info(f"已连接到Milvus服务器 {MILVUS_HOST}:{MILVUS_PORT}")

    # 2. 检查是否存在旧集合，如果度量类型不匹配则重建
    if utility.has_collection(COLLECTION_NAME):
        col = Collection(COLLECTION_NAME)  # 获取现有集合
        try:
            idx = col.index()  # 获取索引信息
            # 检查度量类型是否为内积(IP)
            if idx.params.get("metric_type") != "IP":
                logging.warning("旧集合度量类型不是IP，删除后重建...")
                col.drop()  # 删除集合
                utility.drop_collection(COLLECTION_NAME)  # 从元数据中删除
            else:
                # 集合符合要求，直接加载并返回
                col.load()
                logging.info(f"使用现有集合 {COLLECTION_NAME}")
                return col
        except Exception as e:
            # 如果获取索引信息失败，可能是旧版本集合，删除重建
            logging.warning(f"检查集合索引失败，删除重建: {e}")
            col.drop()
            utility.drop_collection(COLLECTION_NAME)

    # 3. 定义集合字段结构
    fields = [
        # 主键字段：自动生成的唯一ID
        FieldSchema("id", DataType.INT64, is_primary=True, auto_id=True),
        # 文本字段：存储原始文档文本，最大长度65535字符
        FieldSchema("text", DataType.VARCHAR, max_length=65_535),
        # 向量字段：存储BGE-M3生成的1024维嵌入向量
        FieldSchema("vector", DataType.FLOAT_VECTOR, dim=DIM)
    ]

    # 4. 创建集合模式和集合
    schema = CollectionSchema(fields, "智能文档问答系统向量集合")
    col = Collection(COLLECTION_NAME, schema)
    logging.info(f"成功创建新集合 {COLLECTION_NAME}")

    # 5. 创建优化的HNSW索引
    index_params = {
        "index_type": "HNSW",    # 使用HNSW(Hierarchical Navigable Small World)索引
        "metric_type": "IP",     # 使用内积(Inner Product)作为相似度度量
        "params": {
            "M": 48,             # 每个节点的最大连接数，增加以提高召回率
            "efConstruction": 400 # 构建时的搜索深度，增加以提高索引质量
        }
    }
    col.create_index("vector", index_params)  # 在向量字段上创建索引
    logging.info("成功创建优化的HNSW索引 (M=48, efConstruction=400)")

    # 6. 加载集合到内存以支持搜索
    col.load()
    logging.info(f"集合 {COLLECTION_NAME} 已加载到内存，可以进行搜索")

    return col


def store_vectors(texts, embeddings, collection: Collection):
    """
    存储文本和对应的向量到Milvus数据库
    包含完整的数据验证和错误处理

    Args:
        texts: 文本列表
        embeddings: 对应的嵌入向量列表或numpy数组
        collection: Milvus集合对象

    Returns:
        dict: 存储结果，包含状态、数量等信息
    """
    try:
        # 1. 数据格式标准化
        # 确保embeddings是正确的numpy数组格式
        if isinstance(embeddings, list):
            embeddings = np.array(embeddings, dtype=np.float32)  # 转换列表为numpy数组
        elif isinstance(embeddings, np.ndarray):
            embeddings = embeddings.astype(np.float32)  # 确保数据类型为float32
        else:
            raise ValueError(f"不支持的嵌入向量类型: {type(embeddings)}")

        # 2. 数据完整性验证
        # 检查文本数量与向量数量是否匹配
        if len(texts) != len(embeddings):
            raise ValueError(f"文本数量({len(texts)})与向量数量({len(embeddings)})不匹配")

        # 检查是否有数据需要存储
        if len(embeddings) == 0:
            raise ValueError("没有向量数据需要存储")

        # 3. 向量维度验证
        expected_dim = DIM  # 期望的向量维度
        # 获取实际向量维度
        actual_dim = embeddings.shape[1] if len(embeddings.shape) > 1 else len(embeddings[0])
        if actual_dim != expected_dim:
            raise ValueError(f"向量维度不匹配: 期望{expected_dim}, 实际{actual_dim}")

        # 4. 准备插入数据
        # Milvus要求数据格式为[字段1数据, 字段2数据, ...]
        data = [texts, embeddings.tolist()]  # 转换numpy数组为列表

        # 5. 记录存储信息
        logging.info(f"开始存储 {len(texts)} 个向量到Milvus...")
        logging.info(f"向量维度: {actual_dim}")
        logging.info(f"文本样例: {texts[0][:100]}..." if texts[0] else "空文本")

        # 6. 执行数据插入
        mr = collection.insert(data)  # 插入数据到集合

        # 7. 强制刷新确保数据持久化
        collection.flush()  # 将内存中的数据写入磁盘

        # 8. 验证插入结果
        # 获取实际插入的数据数量
        if hasattr(mr, 'insert_count'):
            inserted_count = mr.insert_count  # 从插入结果获取数量
        else:
            inserted_count = len(texts)  # 回退到输入数据数量

        # 9. 获取集合统计信息
        collection.load()  # 确保集合已加载到内存
        total_entities = collection.num_entities  # 获取集合中的总实体数

        # 10. 记录成功信息
        logging.info(f"成功存储 {inserted_count} 个向量")
        logging.info(f"集合总实体数: {total_entities}")

        # 11. 返回成功结果
        return {
            "status": "success",           # 状态：成功
            "count": inserted_count,       # 插入数量
            "total_entities": total_entities,  # 集合总数量
            "vector_dimension": actual_dim,    # 向量维度
            "collection_name": collection.name # 集合名称
        }

    except Exception as e:
        # 12. 错误处理
        error_msg = f"存储向量失败: {str(e)}"
        logging.error(error_msg, exc_info=True)  # 记录详细错误信息

        # 返回错误结果
        return {
            "status": "error",              # 状态：错误
            "message": error_msg,           # 错误消息
            "error_type": type(e).__name__  # 错误类型
        }


def get_collection_stats(collection: Collection):
    """
    获取Milvus集合的详细统计信息
    包括实体数量、字段结构、索引信息等

    Args:
        collection: Milvus集合对象

    Returns:
        dict: 集合统计信息字典
    """
    try:
        # 1. 确保集合已加载到内存
        collection.load()

        # 2. 构建基本统计信息
        stats = {
            "name": collection.name,              # 集合名称
            "num_entities": collection.num_entities,  # 实体总数
            "schema": {
                "fields": [
                    {
                        "name": field.name,                    # 字段名称
                        "type": field.dtype.name,              # 字段数据类型
                        "is_primary": field.is_primary,        # 是否为主键
                        "auto_id": field.auto_id if hasattr(field, 'auto_id') else False  # 是否自动生成ID
                    }
                    for field in collection.schema.fields  # 遍历所有字段
                ]
            }
        }

        # 3. 获取索引信息
        try:
            indexes = collection.indexes  # 获取集合的所有索引
            stats["indexes"] = [
                {
                    "field_name": idx.field_name,                    # 索引字段名
                    "index_type": idx.params.get("index_type"),      # 索引类型(如HNSW)
                    "metric_type": idx.params.get("metric_type"),    # 度量类型(如IP)
                    "params": idx.params                             # 索引参数
                }
                for idx in indexes  # 遍历所有索引
            ]
            logging.info(f"成功获取 {len(indexes)} 个索引信息")
        except Exception as idx_error:
            # 如果获取索引信息失败，记录警告但不中断程序
            logging.warning(f"获取索引信息失败: {idx_error}")
            stats["indexes"] = []  # 设置为空列表

        # 4. 记录统计信息获取成功
        logging.info(f"成功获取集合 {collection.name} 的统计信息")
        return stats

    except Exception as e:
        # 5. 错误处理
        error_msg = f"获取集合统计信息失败: {e}"
        logging.error(error_msg)
        return {"error": error_msg}  # 返回错误信息