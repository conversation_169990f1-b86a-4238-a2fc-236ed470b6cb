# 智能文档问答系统 - 部署指南

## 🚀 快速开始

### 1. 环境准备

#### 系统要求
- **操作系统**: Windows 10/11, Ubuntu 18.04+, CentOS 7+, macOS 10.15+
- **Python**: 3.8 或更高版本
- **内存**: 8GB+ (推荐16GB)
- **存储**: 20GB+ 可用空间
- **网络**: 稳定的互联网连接（用于下载模型）

#### 必需服务
- **Redis**: 6.0+
- **Milvus**: 2.3+
- **Tesseract OCR**: 4.0+

### 2. 安装步骤

#### Step 1: 克隆项目
```bash
git clone <repository-url>
cd RAG工单/招股（3）
```

#### Step 2: 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

#### Step 3: 安装Python依赖
```bash
pip install -r requirements.txt
```

#### Step 4: 安装系统依赖

##### Ubuntu/Debian
```bash
# 更新包列表
sudo apt update

# 安装Redis
sudo apt install redis-server

# 安装Tesseract OCR
sudo apt install tesseract-ocr tesseract-ocr-chi-sim

# 安装OpenCV依赖
sudo apt install libgl1-mesa-glx libglib2.0-0
```

##### CentOS/RHEL
```bash
# 安装EPEL仓库
sudo yum install epel-release

# 安装Redis
sudo yum install redis

# 安装Tesseract OCR
sudo yum install tesseract tesseract-langpack-chi-sim

# 安装OpenCV依赖
sudo yum install mesa-libGL glib2
```

##### Windows
```bash
# 使用Chocolatey安装Redis
choco install redis-64

# 下载并安装Tesseract OCR
# https://github.com/UB-Mannheim/tesseract/wiki
# 确保添加到PATH环境变量
```

##### macOS
```bash
# 使用Homebrew安装
brew install redis tesseract tesseract-lang
```

#### Step 5: 安装Milvus

##### 使用Docker (推荐)
```bash
# 下载Milvus配置文件
wget https://github.com/milvus-io/milvus/releases/download/v2.3.4/milvus-standalone-docker-compose.yml -O docker-compose.yml

# 启动Milvus
docker-compose up -d

# 检查状态
docker-compose ps
```

##### 使用Milvus Lite (轻量版)
```bash
pip install milvus-lite
```

### 3. 模型下载

#### 下载AI模型
```bash
# 创建模型目录
mkdir -p models/BAAI

# 下载BGE-M3嵌入模型
git clone https://huggingface.co/BAAI/bge-m3 models/BAAI/bge-m3

# 下载BGE-Reranker-v2-M3重排序模型
git clone https://huggingface.co/BAAI/bge-reranker-v2-m3 models/BAAI/bge-reranker-v2-m3
```

#### 配置模型路径
编辑 `config.py` 文件，更新模型路径：
```python
MODEL_CONFIG = {
    'EMBEDDING_MODEL_PATH': './models/BAAI/bge-m3',
    'RERANK_MODEL_PATH': './models/BAAI/bge-reranker-v2-m3',
    # ...
}
```

### 4. 配置服务

#### 启动Redis
```bash
# Linux/macOS
sudo systemctl start redis
sudo systemctl enable redis

# Windows
redis-server

# 验证Redis
redis-cli ping
```

#### 配置环境变量
创建 `.env` 文件：
```bash
# Flask配置
FLASK_DEBUG=False
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Milvus配置
MILVUS_HOST=localhost
MILVUS_PORT=19530

# 模型配置
EMBEDDING_MODEL_PATH=./models/BAAI/bge-m3
RERANK_MODEL_PATH=./models/BAAI/bge-reranker-v2-m3
LLM_API_KEY=your-api-key-here

# 日志配置
LOG_LEVEL=INFO
```

### 5. 启动应用

#### 使用启动脚本 (推荐)
```bash
python start.py
```

#### 手动启动
```bash
python app.py
```

#### 使用Gunicorn (生产环境)
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 6. 验证部署

#### 运行测试脚本
```bash
python test_system.py
```

#### 手动验证
1. 访问 http://localhost:5000
2. 上传测试文档
3. 进行问答测试
4. 检查语音功能
5. 测试多轮对话

## 🔧 生产环境部署

### 1. 使用Nginx反向代理

#### 安装Nginx
```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

#### 配置Nginx
创建 `/etc/nginx/sites-available/rag-system`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 支持WebSocket (如果需要)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 文件上传大小限制
        client_max_body_size 100M;
    }
}
```

#### 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/rag-system /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 2. 使用SSL证书

#### 使用Let's Encrypt
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 使用Systemd服务

创建 `/etc/systemd/system/rag-system.service`:
```ini
[Unit]
Description=RAG Document QA System
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/project
Environment=PATH=/path/to/your/project/venv/bin
ExecStart=/path/to/your/project/venv/bin/gunicorn -w 4 -b 127.0.0.1:5000 app:app
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 启动服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable rag-system
sudo systemctl start rag-system
sudo systemctl status rag-system
```

### 4. 监控和日志

#### 使用Supervisor
```bash
pip install supervisor

# 创建配置文件
sudo echo_supervisord_conf > /etc/supervisor/conf.d/supervisord.conf
```

#### 日志轮转
创建 `/etc/logrotate.d/rag-system`:
```
/path/to/your/project/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

## 🐳 Docker部署

### 1. 创建Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    tesseract-ocr-chi-sim \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# 创建必要目录
RUN mkdir -p uploads logs

EXPOSE 5000

CMD ["python", "app.py"]
```

### 2. 创建docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - REDIS_HOST=redis
      - MILVUS_HOST=milvus
    depends_on:
      - redis
      - milvus
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./models:/app/models

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  milvus:
    image: milvusdb/milvus:v2.3.4
    ports:
      - "19530:19530"
    environment:
      - ETCD_ENDPOINTS=etcd:2379
      - MINIO_ADDRESS=minio:9000
    depends_on:
      - etcd
      - minio

  etcd:
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=4294967296
      - ETCD_SNAPSHOT_COUNT=50000

  minio:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    command: minio server /minio_data
```

### 3. 构建和运行
```bash
docker-compose up -d
```

## 🔍 故障排除

### 常见问题及解决方案

1. **端口占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :5000
   # 杀死进程
   kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   # 修改文件权限
   chmod -R 755 /path/to/project
   chown -R www-data:www-data /path/to/project
   ```

3. **内存不足**
   - 增加系统内存
   - 调整模型批处理大小
   - 使用模型量化

4. **模型加载失败**
   - 检查模型路径
   - 验证模型文件完整性
   - 确保有足够磁盘空间

## 📊 性能优化

### 1. 系统级优化
- 使用SSD存储
- 增加内存容量
- 使用GPU加速

### 2. 应用级优化
- 启用Redis缓存
- 调整批处理大小
- 优化索引参数

### 3. 网络优化
- 使用CDN
- 启用Gzip压缩
- 优化静态资源

---

如有部署问题，请参考README.md或联系技术支持。
