# -*- coding: utf-8 -*-
"""
向量搜索模块 - 实现混合检索算法
包含向量检索、关键词匹配、语义加权等功能
"""

# 导入必要的库
import logging  # 日志记录
import numpy as np  # 数值计算
import re  # 正则表达式
from typing import List, Dict, Tuple  # 类型提示
from pymilvus import Collection  # Milvus向量数据库
from collections import Counter  # 计数器
try:
    import jieba  # 中文分词
    import jieba.analyse  # 关键词提取
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False


def extract_keywords(text: str, top_k: int = 10) -> List[str]:
    """
    从文本中提取关键词用于混合检索

    Args:
        text: 输入文本
        top_k: 提取关键词数量

    Returns:
        关键词列表
    """
    try:
        if JIEBA_AVAILABLE:
            # 使用jieba的TF-IDF算法提取关键词
            keywords = jieba.analyse.extract_tags(text, topK=top_k, withWeight=False)
            return keywords
        else:
            # 简单实现：使用空格和标点分词
            import re
            # 使用正则表达式分词，包括中文字符
            words = re.findall(r'[\w\u4e00-\u9fff]+', text)
            # 简单的词频统计
            word_freq = {}
            for word in words:
                if len(word) > 1:  # 过滤单字符
                    word_freq[word] = word_freq.get(word, 0) + 1
            # 按频率排序并返回前top_k个
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            keywords = [word for word, freq in sorted_words[:top_k]]
            return keywords
    except Exception as e:
        # 记录错误但不中断程序
        logging.warning(f"关键词提取失败: {str(e)}")
        return []


def keyword_match_score(query: str, text: str) -> float:
    """
    计算查询与文本的关键词匹配分数

    Args:
        query: 查询文本
        text: 目标文本

    Returns:
        匹配分数 (0-1之间)
    """
    try:
        # 提取查询和文本的关键词
        query_keywords = set(extract_keywords(query, top_k=5))
        text_keywords = set(extract_keywords(text, top_k=10))

        # 如果查询没有关键词，返回0分
        if not query_keywords:
            return 0.0

        # 计算关键词交集比例
        intersection = query_keywords.intersection(text_keywords)
        keyword_score = len(intersection) / len(query_keywords)

        # 考虑直接文本匹配（不区分大小写）
        query_lower = query.lower()
        text_lower = text.lower()
        direct_matches = sum(1 for word in query_keywords if word.lower() in text_lower)
        direct_score = direct_matches / len(query_keywords) if query_keywords else 0

        # 取两种匹配方式的最大值，并应用权重
        return max(keyword_score, direct_score) * 0.3  # 关键词权重为0.3
    except Exception as e:
        # 记录错误但不中断程序
        logging.warning(f"关键词匹配计算失败: {str(e)}")
        return 0.0


def semantic_similarity_boost(query: str, text: str) -> float:
    """
    基于语义相似性的加权计算
    根据内容类型和查询意图给予额外加分

    Args:
        query: 查询文本
        text: 目标文本

    Returns:
        语义加权分数
    """
    try:
        # 初始化加权分数
        boost_score = 0.0

        # 表格内容加权：如果文本是表格且查询涉及数据相关词汇
        if "表格内容：" in text and any(keyword in query for keyword in ["表", "数据", "统计", "金额", "比例"]):
            boost_score += 0.1

        # 图片内容加权：如果文本是图片且查询涉及图像相关词汇
        if "图片" in text and "内容：" in text and any(keyword in query for keyword in ["图", "图表", "图像"]):
            boost_score += 0.1

        # 标题内容加权：如果是短标题文本
        if any(marker in text for marker in ["第", "章", "节", "部分"]) and len(text) < 200:
            boost_score += 0.05

        return boost_score
    except Exception as e:
        # 记录错误但不中断程序
        logging.warning(f"语义相似性加权失败: {str(e)}")
        return 0.0


def search_similar(query, collection: Collection, embedding_generator, top_k=15):
    """
    增强的混合检索算法
    结合向量检索、关键词匹配和语义加权

    Args:
        query: 查询文本
        collection: Milvus集合对象
        embedding_generator: 嵌入向量生成器
        top_k: 返回结果数量

    Returns:
        检索结果列表，包含文本、分数等信息
    """
    try:
        # 1. 生成查询向量
        q_emb = embedding_generator([query])  # 调用嵌入模型生成向量
        if isinstance(q_emb, list):
            q_emb = np.array(q_emb, dtype="float32")  # 转换为numpy数组

        # 2. 配置向量搜索参数
        search_params = {"metric_type": "IP", "params": {"ef": 256}}  # 内积距离，提高ef值增加精度

        # 3. 执行向量检索
        res = collection.search(
            data=[q_emb[0].tolist()],  # 查询向量
            anns_field="vector",  # 向量字段名
            param=search_params,  # 搜索参数
            limit=min(top_k * 2, 30),  # 获取更多候选结果用于重排序
            output_fields=["text"]  # 返回文本字段
        )

        # 4. 混合评分计算
        enhanced_results = []
        for hit in res[0]:  # 遍历搜索结果
            text = hit.entity.get("text", "")  # 获取文本内容
            vector_score = float(hit.score)  # 向量相似度分数

            # 计算关键词匹配分数
            keyword_score = keyword_match_score(query, text)

            # 计算语义相似性加权
            semantic_boost = semantic_similarity_boost(query, text)

            # 综合评分：向量相似度(70%) + 关键词匹配(20%) + 语义加权(10%)
            final_score = vector_score * 0.7 + keyword_score * 0.2 + semantic_boost * 0.1

            # 构建结果对象
            enhanced_results.append({
                "text": text,  # 文本内容
                "score": final_score,  # 最终分数
                "id": hit.id,  # 文档ID
                "vector_score": vector_score,  # 向量分数
                "keyword_score": keyword_score,  # 关键词分数
                "semantic_boost": semantic_boost  # 语义加权分数
            })

        # 5. 按综合分数重新排序
        enhanced_results.sort(key=lambda x: x["score"], reverse=True)

        # 6. 返回top_k结果
        return enhanced_results[:top_k]

    except Exception as e:
        # 记录详细错误信息
        logging.error("Enhanced vector search error", exc_info=True)
        return []


def search_with_filters(query: str, collection: Collection, embedding_generator,
                       content_type: str = None, top_k: int = 15) -> List[Dict]:
    """
    带内容类型过滤的搜索功能
    支持按文本、表格、图片类型过滤搜索结果

    Args:
        query: 查询文本
        collection: Milvus集合对象
        embedding_generator: 嵌入向量生成器
        content_type: 内容类型过滤器 ("text", "table", "image", "all")
        top_k: 返回结果数量

    Returns:
        过滤后的检索结果列表
    """
    try:
        # 1. 先进行常规混合搜索，获取更多候选结果
        results = search_similar(query, collection, embedding_generator, top_k * 2)

        # 2. 如果没有指定内容类型，直接返回结果
        if not content_type or content_type == "all":
            return results[:top_k]

        # 3. 根据内容类型过滤结果
        filtered_results = []
        for result in results:
            text = result["text"]  # 获取文本内容

            # 表格内容过滤
            if content_type == "table" and "表格内容：" in text:
                filtered_results.append(result)
            # 图片内容过滤
            elif content_type == "image" and "图片" in text and "内容：" in text:
                filtered_results.append(result)
            # 纯文本内容过滤（排除表格和图片）
            elif content_type == "text" and "表格内容：" not in text and not ("图片" in text and "内容：" in text):
                filtered_results.append(result)

        # 4. 返回过滤后的top_k结果
        return filtered_results[:top_k]

    except Exception as e:
        # 如果过滤失败，回退到常规搜索
        logging.error(f"Filtered search error: {str(e)}")
        return search_similar(query, collection, embedding_generator, top_k)