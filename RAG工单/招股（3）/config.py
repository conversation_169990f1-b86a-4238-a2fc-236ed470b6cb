# -*- coding: utf-8 -*-
"""
智能文档问答系统配置文件
"""

import os
from pathlib import Path

# 基础配置
BASE_DIR = Path(__file__).parent
UPLOAD_FOLDER = BASE_DIR / 'uploads'
LOGS_FOLDER = BASE_DIR / 'logs'

# 确保目录存在
UPLOAD_FOLDER.mkdir(exist_ok=True)
LOGS_FOLDER.mkdir(exist_ok=True)

# Flask配置
FLASK_CONFIG = {
    'SECRET_KEY': os.environ.get('SECRET_KEY', 'your-secret-key-here'),
    'DEBUG': os.environ.get('FLASK_DEBUG', 'True').lower() == 'true',
    'HOST': os.environ.get('FLASK_HOST', '0.0.0.0'),
    'PORT': int(os.environ.get('FLASK_PORT', 5000)),
    'UPLOAD_FOLDER': str(UPLOAD_FOLDER),
    'MAX_CONTENT_LENGTH': 100 * 1024 * 1024,  # 100MB
}

# 文件处理配置
FILE_CONFIG = {
    'ALLOWED_EXTENSIONS': {'txt', 'pdf'},
    'MAX_FILE_SIZE': 50 * 1024 * 1024,  # 50MB
    'CHUNK_SIZE': 800,  # 文本分块大小
    'MIN_CHUNK_SIZE': 300,  # 最小分块大小
    'CHUNK_OVERLAP': 100,  # 分块重叠大小
}

# Milvus配置
MILVUS_CONFIG = {
    'HOST': os.environ.get('MILVUS_HOST', 'localhost'),
    'PORT': int(os.environ.get('MILVUS_PORT', 19530)),
    'COLLECTION_NAME': 'document_vectors',
    'DIMENSION': 1024,
    'INDEX_TYPE': 'HNSW',
    'METRIC_TYPE': 'IP',
    'INDEX_PARAMS': {
        'M': 48,
        'efConstruction': 400
    },
    'SEARCH_PARAMS': {
        'ef': 256
    }
}

# Redis配置
REDIS_CONFIG = {
    'HOST': os.environ.get('REDIS_HOST', 'localhost'),
    'PORT': int(os.environ.get('REDIS_PORT', 6379)),
    'DB': int(os.environ.get('REDIS_DB', 0)),
    'PASSWORD': os.environ.get('REDIS_PASSWORD', None),
    'SESSION_EXPIRE': 1800,  # 30分钟
    'MAX_HISTORY_LENGTH': 20,  # 最大对话历史长度
}

# AI模型配置
MODEL_CONFIG = {
    'EMBEDDING_MODEL_PATH': os.environ.get(
        'EMBEDDING_MODEL_PATH', 
        r'D:\model\BAAI\bge-m3'
    ),
    'RERANK_MODEL_PATH': os.environ.get(
        'RERANK_MODEL_PATH', 
        r'D:\model\BAAI\bge-reranker-v2-m3'
    ),
    'LLM_API_KEY': os.environ.get(
        'LLM_API_KEY', 
        'ms-6f7de516-7098-44af-969d-b1d28d65ec00'
    ),
    'LLM_BASE_URL': os.environ.get(
        'LLM_BASE_URL', 
        'https://api-inference.modelscope.cn/v1/'
    ),
    'LLM_MODEL': os.environ.get(
        'LLM_MODEL', 
        'Qwen/Qwen2.5-Coder-32B-Instruct'
    ),
    'LLM_TIMEOUT': int(os.environ.get('LLM_TIMEOUT', 30)),
}

# 语音服务配置
SPEECH_CONFIG = {
    'STT_SERVICE': os.environ.get('STT_SERVICE', 'local'),  # local, baidu, azure
    'TTS_SERVICE': os.environ.get('TTS_SERVICE', 'local'),  # local, baidu, azure
    'SAMPLE_RATE': int(os.environ.get('SPEECH_SAMPLE_RATE', 16000)),
    'CHANNELS': int(os.environ.get('SPEECH_CHANNELS', 1)),
    'CHUNK_DURATION': int(os.environ.get('SPEECH_CHUNK_DURATION', 30)),
    'BAIDU_API_KEY': os.environ.get('BAIDU_API_KEY', ''),
    'BAIDU_SECRET_KEY': os.environ.get('BAIDU_SECRET_KEY', ''),
    'AZURE_SPEECH_KEY': os.environ.get('AZURE_SPEECH_KEY', ''),
    'AZURE_SPEECH_REGION': os.environ.get('AZURE_SPEECH_REGION', ''),
}

# 检索配置
SEARCH_CONFIG = {
    'TOP_K': int(os.environ.get('SEARCH_TOP_K', 20)),
    'RERANK_TOP_N': int(os.environ.get('RERANK_TOP_N', 6)),
    'VECTOR_WEIGHT': float(os.environ.get('VECTOR_WEIGHT', 0.7)),
    'KEYWORD_WEIGHT': float(os.environ.get('KEYWORD_WEIGHT', 0.2)),
    'SEMANTIC_WEIGHT': float(os.environ.get('SEMANTIC_WEIGHT', 0.1)),
    'RERANK_WEIGHT': float(os.environ.get('RERANK_WEIGHT', 0.6)),
    'ORIGINAL_WEIGHT': float(os.environ.get('ORIGINAL_WEIGHT', 0.4)),
}

# OCR配置
OCR_CONFIG = {
    'TESSERACT_CMD': os.environ.get('TESSERACT_CMD', None),  # 自动检测
    'LANGUAGES': os.environ.get('OCR_LANGUAGES', 'chi_sim+eng'),
    'CONFIG': os.environ.get('OCR_CONFIG', '--psm 6'),
    'MIN_TEXT_LENGTH': int(os.environ.get('OCR_MIN_TEXT_LENGTH', 100)),
    'IMAGE_ENHANCEMENT': os.environ.get('OCR_IMAGE_ENHANCEMENT', 'True').lower() == 'true',
}

# 日志配置
LOGGING_CONFIG = {
    'LEVEL': os.environ.get('LOG_LEVEL', 'INFO'),
    'FORMAT': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'FILE_PATH': str(LOGS_FOLDER / 'app.log'),
    'MAX_BYTES': 10 * 1024 * 1024,  # 10MB
    'BACKUP_COUNT': 5,
}

# 性能配置
PERFORMANCE_CONFIG = {
    'ENABLE_CACHE': os.environ.get('ENABLE_CACHE', 'True').lower() == 'true',
    'CACHE_TTL': int(os.environ.get('CACHE_TTL', 3600)),  # 1小时
    'MAX_WORKERS': int(os.environ.get('MAX_WORKERS', 4)),
    'BATCH_SIZE': int(os.environ.get('BATCH_SIZE', 32)),
}

# 安全配置
SECURITY_CONFIG = {
    'ENABLE_RATE_LIMIT': os.environ.get('ENABLE_RATE_LIMIT', 'True').lower() == 'true',
    'RATE_LIMIT': os.environ.get('RATE_LIMIT', '100/hour'),
    'ALLOWED_HOSTS': os.environ.get('ALLOWED_HOSTS', '*').split(','),
    'CORS_ORIGINS': os.environ.get('CORS_ORIGINS', '*').split(','),
}

# 开发配置
DEV_CONFIG = {
    'MOCK_SERVICES': os.environ.get('MOCK_SERVICES', 'False').lower() == 'true',
    'DEBUG_SQL': os.environ.get('DEBUG_SQL', 'False').lower() == 'true',
    'PROFILE': os.environ.get('PROFILE', 'False').lower() == 'true',
}

def get_config():
    """获取完整配置"""
    return {
        'flask': FLASK_CONFIG,
        'file': FILE_CONFIG,
        'milvus': MILVUS_CONFIG,
        'redis': REDIS_CONFIG,
        'model': MODEL_CONFIG,
        'speech': SPEECH_CONFIG,
        'search': SEARCH_CONFIG,
        'ocr': OCR_CONFIG,
        'logging': LOGGING_CONFIG,
        'performance': PERFORMANCE_CONFIG,
        'security': SECURITY_CONFIG,
        'dev': DEV_CONFIG,
    }

def validate_config():
    """验证配置"""
    errors = []
    
    # 检查必要的模型路径
    if not os.path.exists(MODEL_CONFIG['EMBEDDING_MODEL_PATH']):
        errors.append(f"嵌入模型路径不存在: {MODEL_CONFIG['EMBEDDING_MODEL_PATH']}")
    
    if not os.path.exists(MODEL_CONFIG['RERANK_MODEL_PATH']):
        errors.append(f"重排序模型路径不存在: {MODEL_CONFIG['RERANK_MODEL_PATH']}")
    
    # 检查API密钥
    if not MODEL_CONFIG['LLM_API_KEY']:
        errors.append("LLM API密钥未设置")
    
    # 检查权重配置
    weights_sum = (SEARCH_CONFIG['VECTOR_WEIGHT'] + 
                   SEARCH_CONFIG['KEYWORD_WEIGHT'] + 
                   SEARCH_CONFIG['SEMANTIC_WEIGHT'])
    if abs(weights_sum - 1.0) > 0.01:
        errors.append(f"搜索权重总和应为1.0，当前为{weights_sum}")
    
    return errors

if __name__ == "__main__":
    # 配置验证
    errors = validate_config()
    if errors:
        print("配置错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
    
    # 打印配置信息
    config = get_config()
    print("\n当前配置:")
    for section, settings in config.items():
        print(f"\n[{section.upper()}]")
        for key, value in settings.items():
            # 隐藏敏感信息
            if 'key' in key.lower() or 'password' in key.lower():
                value = '*' * len(str(value)) if value else 'None'
            print(f"  {key}: {value}")
