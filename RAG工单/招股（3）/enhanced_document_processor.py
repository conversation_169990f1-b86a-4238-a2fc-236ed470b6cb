# -*- coding: utf-8 -*-
"""
增强的文档处理模块
实现语义感知的文档分割、多模态内容提取和优化的处理流程
"""

import os
import re
import logging
import time
import hashlib
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from pathlib import Path

# 文档处理库
import fitz  # PyMuPDF
import pandas as pd
from PIL import Image, ImageEnhance, ImageFilter
import cv2
import numpy as np

# OCR库
import easyocr
import pytesseract

# 文本处理
import jieba
from sentence_transformers import SentenceTransformer

# 配置
from config import get_config

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class DocumentChunk:
    """文档块数据结构"""
    content: str  # 文本内容
    chunk_id: str  # 块ID
    source_file: str  # 源文件
    page_number: int  # 页码
    chunk_type: str  # 块类型：text, table, image
    metadata: Dict[str, Any]  # 元数据
    embedding: Optional[List[float]] = None  # 嵌入向量
    semantic_score: float = 0.0  # 语义连贯性分数

@dataclass
class ProcessingProgress:
    """处理进度跟踪"""
    total_pages: int = 0
    processed_pages: int = 0
    total_chunks: int = 0
    processed_chunks: int = 0
    current_stage: str = "初始化"
    start_time: float = 0.0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.start_time == 0.0:
            self.start_time = time.time()

class SemanticTextSplitter:
    """语义感知的文本分割器"""
    
    def __init__(self, embedding_model: SentenceTransformer = None):
        """初始化分割器"""
        self.config = get_config()
        self.embedding_model = embedding_model
        self.logger = logging.getLogger(__name__)
        
        # 分割参数
        self.chunk_size = self.config['file']['CHUNK_SIZE']
        self.min_chunk_size = self.config['file']['MIN_CHUNK_SIZE']
        self.chunk_overlap = self.config['file']['CHUNK_OVERLAP']
        
        # 语义分割阈值
        self.semantic_threshold = 0.7
        
        # 句子分割模式
        self.sentence_patterns = [
            r'[。！？；]',  # 中文句号、感叹号、问号、分号
            r'[.!?;]',     # 英文标点
            r'\n\n',       # 段落分隔
        ]
    
    def split_text_semantically(self, text: str, metadata: Dict = None) -> List[DocumentChunk]:
        """语义感知的文本分割"""
        if not text or len(text) < self.min_chunk_size:
            return []
        
        try:
            # 1. 预处理文本
            cleaned_text = self._preprocess_text(text)
            
            # 2. 句子分割
            sentences = self._split_into_sentences(cleaned_text)
            
            # 3. 语义分组
            if self.embedding_model and len(sentences) > 1:
                chunks = self._semantic_grouping(sentences, metadata)
            else:
                chunks = self._simple_chunking(sentences, metadata)
            
            # 4. 后处理
            chunks = self._post_process_chunks(chunks, metadata)
            
            self.logger.info(f"文本分割完成: {len(text)} 字符 -> {len(chunks)} 块")
            return chunks
            
        except Exception as e:
            self.logger.error(f"文本分割失败: {e}", exc_info=True)
            return self._fallback_chunking(text, metadata)
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 清理多余空白
        text = re.sub(r'\s+', ' ', text)
        
        # 保留重要的换行符
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # 清理特殊字符但保留标点
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】""''。，；：！？]', '', text)
        
        return text.strip()
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """分割成句子"""
        sentences = []
        
        # 使用多种模式分割
        current_text = text
        for pattern in self.sentence_patterns:
            parts = re.split(pattern, current_text)
            if len(parts) > 1:
                sentences.extend([part.strip() for part in parts if part.strip()])
                break
        
        # 如果没有分割成功，按长度分割
        if not sentences:
            sentences = [text[i:i+200] for i in range(0, len(text), 200)]
        
        return [s for s in sentences if len(s) >= 10]  # 过滤太短的句子
    
    def _semantic_grouping(self, sentences: List[str], metadata: Dict) -> List[DocumentChunk]:
        """基于语义相似性的分组"""
        if not sentences:
            return []
        
        try:
            # 计算句子嵌入
            embeddings = self.embedding_model.encode(sentences)
            
            # 计算相邻句子的相似性
            similarities = []
            for i in range(len(embeddings) - 1):
                sim = np.dot(embeddings[i], embeddings[i + 1])
                similarities.append(sim)
            
            # 基于相似性分组
            chunks = []
            current_chunk = [sentences[0]]
            current_length = len(sentences[0])
            
            for i, sim in enumerate(similarities):
                next_sentence = sentences[i + 1]
                
                # 判断是否应该分割
                should_split = (
                    sim < self.semantic_threshold or  # 语义不连贯
                    current_length + len(next_sentence) > self.chunk_size  # 超过长度限制
                )
                
                if should_split and current_length >= self.min_chunk_size:
                    # 创建新块
                    chunk_content = ' '.join(current_chunk)
                    chunk = self._create_chunk(chunk_content, metadata, len(chunks))
                    chunk.semantic_score = np.mean([similarities[max(0, i-len(current_chunk)):i]])
                    chunks.append(chunk)
                    
                    # 开始新块（带重叠）
                    overlap_sentences = current_chunk[-2:] if len(current_chunk) >= 2 else current_chunk
                    current_chunk = overlap_sentences + [next_sentence]
                    current_length = sum(len(s) for s in current_chunk)
                else:
                    current_chunk.append(next_sentence)
                    current_length += len(next_sentence)
            
            # 处理最后一个块
            if current_chunk and current_length >= self.min_chunk_size:
                chunk_content = ' '.join(current_chunk)
                chunk = self._create_chunk(chunk_content, metadata, len(chunks))
                chunks.append(chunk)
            
            return chunks
            
        except Exception as e:
            self.logger.warning(f"语义分组失败，回退到简单分块: {e}")
            return self._simple_chunking(sentences, metadata)
    
    def _simple_chunking(self, sentences: List[str], metadata: Dict) -> List[DocumentChunk]:
        """简单的基于长度的分块"""
        chunks = []
        current_chunk = []
        current_length = 0
        
        for sentence in sentences:
            if current_length + len(sentence) > self.chunk_size and current_chunk:
                # 创建块
                chunk_content = ' '.join(current_chunk)
                chunk = self._create_chunk(chunk_content, metadata, len(chunks))
                chunks.append(chunk)
                
                # 开始新块（带重叠）
                overlap_size = min(self.chunk_overlap, len(chunk_content))
                overlap_text = chunk_content[-overlap_size:] if overlap_size > 0 else ""
                current_chunk = [overlap_text, sentence] if overlap_text else [sentence]
                current_length = len(overlap_text) + len(sentence)
            else:
                current_chunk.append(sentence)
                current_length += len(sentence)
        
        # 处理最后一个块
        if current_chunk:
            chunk_content = ' '.join(current_chunk)
            if len(chunk_content) >= self.min_chunk_size:
                chunk = self._create_chunk(chunk_content, metadata, len(chunks))
                chunks.append(chunk)
        
        return chunks
    
    def _create_chunk(self, content: str, metadata: Dict, chunk_index: int) -> DocumentChunk:
        """创建文档块"""
        chunk_id = hashlib.md5(f"{content}_{chunk_index}".encode()).hexdigest()[:16]
        
        return DocumentChunk(
            content=content,
            chunk_id=chunk_id,
            source_file=metadata.get('source_file', ''),
            page_number=metadata.get('page_number', 0),
            chunk_type=metadata.get('chunk_type', 'text'),
            metadata={
                **metadata,
                'chunk_index': chunk_index,
                'content_length': len(content),
                'created_at': time.time()
            }
        )
    
    def _post_process_chunks(self, chunks: List[DocumentChunk], metadata: Dict) -> List[DocumentChunk]:
        """后处理块"""
        # 过滤太短的块
        filtered_chunks = [chunk for chunk in chunks if len(chunk.content) >= self.min_chunk_size]
        
        # 重新编号
        for i, chunk in enumerate(filtered_chunks):
            chunk.metadata['final_index'] = i
        
        return filtered_chunks
    
    def _fallback_chunking(self, text: str, metadata: Dict) -> List[DocumentChunk]:
        """回退分块策略"""
        chunks = []
        for i in range(0, len(text), self.chunk_size):
            chunk_text = text[i:i + self.chunk_size]
            if len(chunk_text) >= self.min_chunk_size:
                chunk = self._create_chunk(chunk_text, metadata, len(chunks))
                chunks.append(chunk)
        return chunks

class EnhancedImageProcessor:
    """增强的图像处理器"""
    
    def __init__(self):
        """初始化图像处理器"""
        self.config = get_config()
        self.logger = logging.getLogger(__name__)
        
        # 初始化OCR引擎
        self.easyocr_reader = None
        self._init_ocr_engines()
    
    def _init_ocr_engines(self):
        """初始化OCR引擎"""
        try:
            # 初始化EasyOCR
            self.easyocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            self.logger.info("EasyOCR初始化成功")
        except Exception as e:
            self.logger.warning(f"EasyOCR初始化失败: {e}")
    
    def enhance_image_for_ocr(self, image: Image.Image) -> Image.Image:
        """增强图像以提高OCR准确性"""
        try:
            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 转换为OpenCV格式
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # 1. 去噪
            denoised = cv2.fastNlMeansDenoisingColored(cv_image, None, 10, 10, 7, 21)
            
            # 2. 锐化
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(denoised, -1, kernel)
            
            # 3. 对比度增强
            lab = cv2.cvtColor(sharpened, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            l = clahe.apply(l)
            enhanced = cv2.merge([l, a, b])
            enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
            
            # 4. 二值化（如果需要）
            gray = cv2.cvtColor(enhanced, cv2.COLOR_BGR2GRAY)
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 选择最佳结果
            enhanced_rgb = cv2.cvtColor(enhanced, cv2.COLOR_BGR2RGB)
            binary_rgb = cv2.cvtColor(binary, cv2.COLOR_GRAY2RGB)
            
            # 返回增强后的彩色图像
            return Image.fromarray(enhanced_rgb)
            
        except Exception as e:
            self.logger.warning(f"图像增强失败: {e}")
            return image
    
    def extract_text_from_image(self, image: Image.Image) -> str:
        """从图像中提取文本"""
        try:
            # 增强图像
            enhanced_image = self.enhance_image_for_ocr(image)
            
            # 尝试EasyOCR
            if self.easyocr_reader:
                try:
                    results = self.easyocr_reader.readtext(np.array(enhanced_image))
                    text = ' '.join([result[1] for result in results if result[2] > 0.5])
                    if text.strip():
                        return text.strip()
                except Exception as e:
                    self.logger.warning(f"EasyOCR失败: {e}")
            
            # 回退到Tesseract
            try:
                text = pytesseract.image_to_string(
                    enhanced_image, 
                    lang='chi_sim+eng',
                    config='--psm 6'
                )
                return text.strip()
            except Exception as e:
                self.logger.warning(f"Tesseract OCR失败: {e}")
                return ""
                
        except Exception as e:
            self.logger.error(f"图像文本提取失败: {e}")
            return ""

class EnhancedTableProcessor:
    """增强的表格处理器"""
    
    def __init__(self):
        """初始化表格处理器"""
        self.logger = logging.getLogger(__name__)
    
    def extract_tables_from_page(self, page: fitz.Page) -> List[Dict]:
        """从页面提取表格"""
        tables = []
        
        try:
            # 1. 使用PyMuPDF提取表格
            table_list = page.find_tables()
            
            for table_index, table in enumerate(table_list):
                try:
                    # 提取表格数据
                    table_data = table.extract()
                    
                    if table_data and len(table_data) > 1:  # 至少有标题行和数据行
                        # 转换为DataFrame
                        df = pd.DataFrame(table_data[1:], columns=table_data[0])
                        
                        # 清理数据
                        df = df.fillna('')
                        df = df.astype(str)
                        
                        # 生成表格文本描述
                        table_text = self._format_table_text(df, table_index)
                        
                        # 获取表格位置
                        bbox = table.bbox
                        
                        table_info = {
                            'index': table_index,
                            'data': df.to_dict('records'),
                            'text': table_text,
                            'bbox': bbox,
                            'rows': len(df),
                            'cols': len(df.columns),
                            'metadata': {
                                'extraction_method': 'pymupdf',
                                'confidence': 0.8
                            }
                        }
                        
                        tables.append(table_info)
                        
                except Exception as e:
                    self.logger.warning(f"表格{table_index}处理失败: {e}")
                    continue
            
            self.logger.info(f"页面表格提取完成: {len(tables)} 个表格")
            return tables
            
        except Exception as e:
            self.logger.error(f"表格提取失败: {e}")
            return []
    
    def _format_table_text(self, df: pd.DataFrame, table_index: int) -> str:
        """格式化表格为文本"""
        try:
            # 生成表格描述
            text_parts = [f"表格{table_index + 1}内容："]
            
            # 添加列标题
            headers = list(df.columns)
            text_parts.append(f"列标题：{' | '.join(headers)}")
            
            # 添加数据行
            for i, row in df.iterrows():
                row_text = ' | '.join([str(val) for val in row.values])
                text_parts.append(f"第{i+1}行：{row_text}")
                
                # 限制行数避免过长
                if i >= 10:
                    text_parts.append(f"... (共{len(df)}行)")
                    break
            
            return '\n'.join(text_parts)
            
        except Exception as e:
            self.logger.warning(f"表格文本格式化失败: {e}")
            return f"表格{table_index + 1}：数据提取失败"

class EnhancedDocumentProcessor:
    """增强的文档处理器"""
    
    def __init__(self, embedding_model: SentenceTransformer = None):
        """初始化文档处理器"""
        self.config = get_config()
        self.logger = logging.getLogger(__name__)
        
        # 初始化子处理器
        self.text_splitter = SemanticTextSplitter(embedding_model)
        self.image_processor = EnhancedImageProcessor()
        self.table_processor = EnhancedTableProcessor()
        
        # 处理配置
        self.max_workers = self.config['performance']['MAX_WORKERS']
        self.processing_timeout = 300  # 5分钟超时
    
    def process_document(self, file_path: str, progress_callback=None) -> Tuple[List[DocumentChunk], ProcessingProgress]:
        """处理文档，返回分块结果和进度信息"""
        progress = ProcessingProgress()
        progress.current_stage = "开始处理文档"
        
        try:
            # 确定文件类型
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext == '.pdf':
                return self._process_pdf(file_path, progress, progress_callback)
            elif file_ext in ['.txt']:
                return self._process_text_file(file_path, progress, progress_callback)
            else:
                raise ValueError(f"不支持的文件类型: {file_ext}")
                
        except Exception as e:
            progress.errors.append(f"文档处理失败: {e}")
            self.logger.error(f"文档处理失败: {e}", exc_info=True)
            return [], progress
    
    def _process_pdf(self, file_path: str, progress: ProcessingProgress, progress_callback) -> Tuple[List[DocumentChunk], ProcessingProgress]:
        """处理PDF文件"""
        all_chunks = []
        
        try:
            # 打开PDF
            doc = fitz.open(file_path)
            progress.total_pages = len(doc)
            progress.current_stage = "解析PDF页面"
            
            if progress_callback:
                progress_callback(progress)
            
            # 并行处理页面
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_page = {
                    executor.submit(self._process_pdf_page, doc[page_num], page_num, file_path): page_num
                    for page_num in range(len(doc))
                }
                
                for future in as_completed(future_to_page):
                    page_num = future_to_page[future]
                    try:
                        page_chunks = future.result(timeout=60)  # 每页60秒超时
                        all_chunks.extend(page_chunks)
                        
                        progress.processed_pages += 1
                        progress.total_chunks += len(page_chunks)
                        
                        if progress_callback:
                            progress_callback(progress)
                            
                    except Exception as e:
                        error_msg = f"页面{page_num}处理失败: {e}"
                        progress.errors.append(error_msg)
                        self.logger.warning(error_msg)
            
            doc.close()
            progress.current_stage = "处理完成"
            
            self.logger.info(f"PDF处理完成: {file_path}, 总块数: {len(all_chunks)}")
            return all_chunks, progress
            
        except Exception as e:
            progress.errors.append(f"PDF处理失败: {e}")
            self.logger.error(f"PDF处理失败: {e}", exc_info=True)
            return [], progress
    
    def _process_pdf_page(self, page: fitz.Page, page_num: int, file_path: str) -> List[DocumentChunk]:
        """处理单个PDF页面"""
        chunks = []
        
        try:
            # 基础元数据
            base_metadata = {
                'source_file': file_path,
                'page_number': page_num + 1,
                'processing_time': time.time()
            }
            
            # 1. 提取文本
            text = page.get_text()
            if text.strip():
                text_metadata = {**base_metadata, 'chunk_type': 'text'}
                text_chunks = self.text_splitter.split_text_semantically(text, text_metadata)
                chunks.extend(text_chunks)
            
            # 2. 提取表格
            tables = self.table_processor.extract_tables_from_page(page)
            for table in tables:
                table_metadata = {
                    **base_metadata, 
                    'chunk_type': 'table',
                    'table_info': table['metadata']
                }
                table_chunk = DocumentChunk(
                    content=table['text'],
                    chunk_id=hashlib.md5(f"{file_path}_{page_num}_table_{table['index']}".encode()).hexdigest()[:16],
                    source_file=file_path,
                    page_number=page_num + 1,
                    chunk_type='table',
                    metadata=table_metadata
                )
                chunks.append(table_chunk)
            
            # 3. 提取图像
            image_list = page.get_images()
            for img_index, img in enumerate(image_list):
                try:
                    # 提取图像
                    xref = img[0]
                    pix = fitz.Pixmap(page.parent, xref)
                    
                    if pix.n - pix.alpha < 4:  # 确保是RGB或灰度图
                        # 转换为PIL图像
                        img_data = pix.tobytes("ppm")
                        pil_img = Image.open(io.BytesIO(img_data))
                        
                        # OCR提取文本
                        ocr_text = self.image_processor.extract_text_from_image(pil_img)
                        
                        if ocr_text.strip():
                            image_metadata = {
                                **base_metadata,
                                'chunk_type': 'image',
                                'image_index': img_index
                            }
                            
                            image_chunk = DocumentChunk(
                                content=f"图片{img_index + 1}内容：{ocr_text}",
                                chunk_id=hashlib.md5(f"{file_path}_{page_num}_image_{img_index}".encode()).hexdigest()[:16],
                                source_file=file_path,
                                page_number=page_num + 1,
                                chunk_type='image',
                                metadata=image_metadata
                            )
                            chunks.append(image_chunk)
                    
                    pix = None  # 释放内存
                    
                except Exception as e:
                    self.logger.warning(f"图像{img_index}处理失败: {e}")
                    continue
            
            return chunks
            
        except Exception as e:
            self.logger.error(f"页面{page_num}处理失败: {e}")
            return []
    
    def _process_text_file(self, file_path: str, progress: ProcessingProgress, progress_callback) -> Tuple[List[DocumentChunk], ProcessingProgress]:
        """处理文本文件"""
        try:
            progress.current_stage = "读取文本文件"
            progress.total_pages = 1
            
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
            
            # 分割文本
            metadata = {
                'source_file': file_path,
                'page_number': 1,
                'chunk_type': 'text'
            }
            
            chunks = self.text_splitter.split_text_semantically(text, metadata)
            
            progress.processed_pages = 1
            progress.total_chunks = len(chunks)
            progress.current_stage = "处理完成"
            
            if progress_callback:
                progress_callback(progress)
            
            return chunks, progress
            
        except Exception as e:
            progress.errors.append(f"文本文件处理失败: {e}")
            return [], progress

# 全局实例
enhanced_processor = None

def get_enhanced_processor(embedding_model=None):
    """获取增强文档处理器实例"""
    global enhanced_processor
    if enhanced_processor is None:
        enhanced_processor = EnhancedDocumentProcessor(embedding_model)
    return enhanced_processor
