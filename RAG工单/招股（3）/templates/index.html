<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>智能文档问答系统 - AI助手</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <style>
    :root {
      --primary-color: #3b82f6;
      --secondary-color: #8b5cf6;
      --accent-color: #06b6d4;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --error-color: #ef4444;
      --dark-color: #1f2937;
      --light-color: #f8fafc;
    }

    .gradient-bg {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    }

    .glass-effect {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .chat-bubble {
      animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .pulse-dot {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .voice-recording {
      animation: voicePulse 1s infinite;
    }

    @keyframes voicePulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.1); }
    }

    .sidebar {
      transition: transform 0.3s ease;
    }

    .sidebar.hidden {
      transform: translateX(-100%);
    }

    .content-type-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 0.375rem;
      font-weight: 500;
    }

    .type-text { background-color: #dbeafe; color: #1e40af; }
    .type-table { background-color: #dcfce7; color: #166534; }
    .type-image { background-color: #fef3c7; color: #92400e; }

    .reference-card {
      transition: all 0.2s ease;
    }

    .reference-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>
<<body class="bg-gray-50 font-sans min-h-screen">
  <!-- 侧边栏 -->
  <div id="sidebar" class="sidebar fixed left-0 top-0 h-full w-80 bg-white shadow-xl z-50 overflow-y-auto">
    <div class="p-6 border-b">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-bold text-gray-800">
          <i class="fas fa-robot mr-2 text-blue-500"></i>AI助手
        </h2>
        <button id="closeSidebar" class="text-gray-500 hover:text-gray-700">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- 会话信息 -->
    <div class="p-4 border-b">
      <div class="text-sm text-gray-600 mb-2">当前会话</div>
      <div id="sessionInfo" class="text-xs text-gray-500">
        <div>消息数: <span id="messageCount">0</span></div>
        <div>会话ID: <span id="sessionId">-</span></div>
      </div>
      <button id="clearSession" class="mt-2 text-xs bg-red-100 text-red-600 px-2 py-1 rounded hover:bg-red-200">
        <i class="fas fa-trash mr-1"></i>清除会话
      </button>
    </div>

    <!-- 内容类型过滤 -->
    <div class="p-4 border-b">
      <div class="text-sm text-gray-600 mb-2">内容类型</div>
      <div class="space-y-2">
        <label class="flex items-center">
          <input type="radio" name="contentType" value="all" checked class="mr-2">
          <span class="text-sm">全部内容</span>
        </label>
        <label class="flex items-center">
          <input type="radio" name="contentType" value="text" class="mr-2">
          <span class="text-sm">文本内容</span>
        </label>
        <label class="flex items-center">
          <input type="radio" name="contentType" value="table" class="mr-2">
          <span class="text-sm">表格数据</span>
        </label>
        <label class="flex items-center">
          <input type="radio" name="contentType" value="image" class="mr-2">
          <span class="text-sm">图片内容</span>
        </label>
      </div>
    </div>

    <!-- 语音设置 -->
    <div class="p-4 border-b">
      <div class="text-sm text-gray-600 mb-2">语音功能</div>
      <div class="space-y-2">
        <label class="flex items-center">
          <input type="checkbox" id="enableVoice" class="mr-2">
          <span class="text-sm">启用语音识别</span>
        </label>
        <label class="flex items-center">
          <input type="checkbox" id="enableTTS" class="mr-2">
          <span class="text-sm">启用语音播报</span>
        </label>
      </div>
    </div>

    <!-- 系统状态 -->
    <div class="p-4">
      <div class="text-sm text-gray-600 mb-2">系统状态</div>
      <div id="system-status" class="space-y-2 text-xs">
        <div class="flex items-center justify-between">
          <span>向量数据库:</span>
          <span id="milvus-status" class="text-gray-500">检查中...</span>
        </div>
        <div class="flex items-center justify-between">
          <span>缓存服务:</span>
          <span id="redis-status" class="text-gray-500">检查中...</span>
        </div>
        <div class="flex items-center justify-between">
          <span>AI模型:</span>
          <span id="ai-status" class="text-gray-500">检查中...</span>
        </div>
        <div class="flex items-center justify-between">
          <span>文档数量:</span>
          <span id="doc-count" class="text-gray-500">-</span>
        </div>
      </div>
      <button id="refresh-status" class="mt-2 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded hover:bg-gray-200">
        <i class="fas fa-refresh mr-1"></i>刷新状态
      </button>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="ml-0 transition-all duration-300" id="mainContent">
    <!-- 顶部导航 -->
    <header class="gradient-bg text-white shadow-lg sticky top-0 z-40">
      <div class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <button id="toggleSidebar" class="text-white hover:text-gray-200">
              <i class="fas fa-bars text-xl"></i>
            </button>
            <div class="flex items-center space-x-3">
              <i class="fas fa-brain text-2xl"></i>
              <div>
                <h1 class="text-xl font-bold">智能文档问答系统</h1>
                <p class="text-xs opacity-80">基于BGE-M3 & HNSW & Milvus</p>
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-xs opacity-80">
              <i class="fas fa-circle pulse-dot text-green-400 mr-1"></i>
              在线服务
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主内容 -->
    <main class="container mx-auto px-4 py-8 max-w-6xl">
      <!-- 文件上传区域 -->
      <section class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-bold text-gray-800">
            <i class="fas fa-cloud-upload-alt mr-2 text-blue-500"></i>文档上传
          </h2>
          <div class="text-sm text-gray-500">支持 PDF、TXT 格式</div>
        </div>

        <div id="drop-area" class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-500 hover:bg-blue-50 transition-all duration-300 cursor-pointer">
          <div class="mb-4">
            <i class="fas fa-file-upload text-4xl text-gray-400"></i>
          </div>
          <p class="text-gray-600 mb-2">拖拽文件到此处，或者</p>
          <label class="inline-block bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 cursor-pointer transition-colors">
            <i class="fas fa-plus mr-2"></i>选择文件
            <input id="file-input" type="file" class="hidden" accept=".pdf,.txt">
          </label>
        </div>

        <div id="upload-status" class="mt-4 hidden"></div>

        <!-- 详细进度显示 -->
        <div id="upload-progress-detail" class="mt-4 hidden">
          <div class="bg-white border rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <h4 class="font-medium text-gray-800">处理进度</h4>
              <span id="progress-percentage" class="text-sm text-gray-600">0%</span>
            </div>

            <!-- 总进度条 -->
            <div class="bg-gray-200 rounded-full h-3 mb-4">
              <div id="progress-bar" class="bg-blue-500 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>

            <!-- 当前步骤 -->
            <div class="mb-4">
              <div class="text-sm text-gray-600 mb-1">当前步骤</div>
              <div id="current-step" class="text-sm font-medium text-blue-600">准备中...</div>
            </div>

            <!-- 处理步骤列表 -->
            <div class="space-y-2">
              <div class="text-sm text-gray-600 mb-2">处理步骤:</div>
              <div id="processing-steps" class="space-y-1"></div>
            </div>

            <!-- 性能指标 -->
            <div id="performance-metrics" class="mt-4 hidden">
              <div class="text-sm text-gray-600 mb-2">性能指标:</div>
              <div class="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <span class="text-gray-500">处理时间:</span>
                  <span id="processing-time" class="font-medium text-blue-600">-</span>
                </div>
                <div>
                  <span class="text-gray-500">分块数量:</span>
                  <span id="chunks-count" class="font-medium text-green-600">-</span>
                </div>
                <div>
                  <span class="text-gray-500">向量维度:</span>
                  <span id="embedding-dim" class="font-medium text-purple-600">-</span>
                </div>
                <div>
                  <span class="text-gray-500">处理速度:</span>
                  <span id="processing-speed" class="font-medium text-orange-600">-</span>
                </div>
                <div>
                  <span class="text-gray-500">文件大小:</span>
                  <span id="file-size" class="font-medium text-indigo-600">-</span>
                </div>
                <div>
                  <span class="text-gray-500">存储状态:</span>
                  <span id="storage-status" class="font-medium text-teal-600">-</span>
                </div>
              </div>
            </div>

            <!-- 处理详情 -->
            <div id="processing-details" class="mt-4 hidden">
              <div class="text-sm text-gray-600 mb-2">处理详情:</div>
              <div class="bg-gray-50 rounded-lg p-3 text-xs space-y-1">
                <div class="flex justify-between">
                  <span>文件类型:</span>
                  <span id="detail-file-type" class="font-medium">-</span>
                </div>
                <div class="flex justify-between">
                  <span>页面数量:</span>
                  <span id="detail-pages" class="font-medium">-</span>
                </div>
                <div class="flex justify-between">
                  <span>表格数量:</span>
                  <span id="detail-tables" class="font-medium">-</span>
                </div>
                <div class="flex justify-between">
                  <span>图片数量:</span>
                  <span id="detail-images" class="font-medium">-</span>
                </div>
                <div class="flex justify-between">
                  <span>OCR页面:</span>
                  <span id="detail-ocr-pages" class="font-medium">-</span>
                </div>
                <div class="flex justify-between">
                  <span>识别准确率:</span>
                  <span id="detail-accuracy" class="font-medium">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 聊天界面 -->
      <section class="bg-white rounded-xl shadow-lg overflow-hidden">
        <!-- 聊天头部 -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-bold">
              <i class="fas fa-comments mr-2"></i>智能问答
            </h2>
            <div class="flex items-center space-x-2 text-sm">
              <span id="chatStatus" class="opacity-80">准备就绪</span>
              <i class="fas fa-circle pulse-dot text-green-400"></i>
            </div>
          </div>
        </div>

        <!-- 聊天消息区域 -->
        <div id="chat-messages" class="h-96 overflow-y-auto p-4 bg-gray-50">
          <div class="text-center text-gray-500 py-8">
            <i class="fas fa-robot text-4xl mb-4 text-blue-400"></i>
            <p class="text-lg font-medium mb-2">欢迎使用智能文档问答系统</p>
            <p class="text-sm">上传文档后，您可以向我提问任何相关问题</p>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="border-t bg-white p-4">
          <div class="flex items-center space-x-3">
            <!-- 语音按钮 -->
            <button id="voice-btn" class="flex-shrink-0 w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors" title="语音输入">
              <i class="fas fa-microphone text-gray-600"></i>
            </button>

            <!-- 输入框 -->
            <div class="flex-1 relative">
              <input
                id="query-input"
                type="text"
                placeholder="输入您的问题..."
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              >
              <div id="input-suggestions" class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg hidden z-10"></div>
            </div>

            <!-- 发送按钮 -->
            <button id="send-btn" class="flex-shrink-0 w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex items-center justify-center transition-colors">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>

          <!-- 快捷问题 -->
          <div id="quick-questions" class="mt-3 flex flex-wrap gap-2">
            <button class="quick-question px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors">
              这个文档主要讲什么？
            </button>
            <button class="quick-question px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors">
              有哪些重要数据？
            </button>
            <button class="quick-question px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors">
              总结一下要点
            </button>
          </div>
        </div>
      </section>

      <!-- 参考文档区域 -->
      <div id="references-section" class="mt-8 hidden">
        <div class="bg-white rounded-xl shadow-lg p-6">
          <h3 class="text-lg font-bold text-gray-800 mb-4">
            <i class="fas fa-bookmark mr-2 text-yellow-500"></i>参考文档
          </h3>
          <div id="reference-list" class="space-y-4"></div>
        </div>
      </div>
    </main>
  </div>

  <!-- 底部信息 -->
  <footer class="text-center text-sm text-gray-500 py-6 bg-white border-t">
    <div class="container mx-auto px-4">
      <p>© 2025 智能文档问答系统 · 基于本地AI · 安全可信</p>
      <p class="mt-1 text-xs">支持多轮对话 · 语音交互 · 混合检索</p>
    </div>
  </footer>

  <!-- 语音录制模态框 -->
  <div id="voice-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white rounded-xl p-8 max-w-sm w-full mx-4">
      <div class="text-center">
        <div id="voice-animation" class="w-20 h-20 bg-red-500 rounded-full mx-auto mb-4 flex items-center justify-center voice-recording">
          <i class="fas fa-microphone text-white text-2xl"></i>
        </div>
        <h3 class="text-lg font-bold mb-2">正在录音...</h3>
        <p class="text-gray-600 mb-4">请说出您的问题</p>
        <div class="flex space-x-3">
          <button id="stop-recording" class="flex-1 bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600">
            <i class="fas fa-stop mr-2"></i>停止录音
          </button>
          <button id="cancel-recording" class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400">
            取消
          </button>
        </div>
      </div>
    </div>
  </div>

<script>
// 全局变量
let mediaRecorder = null;
let audioChunks = [];
let isRecording = false;
let currentSessionId = null;
let chatHistory = [];

// DOM 元素
const sidebar = document.getElementById('sidebar');
const mainContent = document.getElementById('mainContent');
const toggleSidebar = document.getElementById('toggleSidebar');
const closeSidebar = document.getElementById('closeSidebar');
const dropArea = document.getElementById('drop-area');
const fileInput = document.getElementById('file-input');
const uploadStatus = document.getElementById('upload-status');
const progressBar = document.getElementById('progress-bar');
const uploadProgress = document.getElementById('upload-progress');
const queryInput = document.getElementById('query-input');
const sendBtn = document.getElementById('send-btn');
const voiceBtn = document.getElementById('voice-btn');
const chatMessages = document.getElementById('chat-messages');
const chatStatus = document.getElementById('chatStatus');
const referencesSection = document.getElementById('references-section');
const referenceList = document.getElementById('reference-list');
const voiceModal = document.getElementById('voice-modal');
const stopRecording = document.getElementById('stop-recording');
const cancelRecording = document.getElementById('cancel-recording');
const clearSession = document.getElementById('clearSession');
const sessionInfo = document.getElementById('sessionInfo');
const messageCount = document.getElementById('messageCount');
const sessionId = document.getElementById('sessionId');
const quickQuestions = document.querySelectorAll('.quick-question');
const refreshStatusBtn = document.getElementById('refresh-status');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadSessionInfo();
});

function initializeApp() {
    // 侧边栏控制
    if (toggleSidebar) {
        toggleSidebar.addEventListener('click', () => {
            sidebar.classList.toggle('hidden');
            mainContent.classList.toggle('ml-80');
        });
    }

    if (closeSidebar) {
        closeSidebar.addEventListener('click', () => {
            sidebar.classList.add('hidden');
            mainContent.classList.remove('ml-80');
        });
    }

    // 文件上传
    setupFileUpload();

    // 聊天功能
    setupChat();

    // 语音功能
    setupVoice();

    // 会话管理
    setupSession();

    // 快捷问题
    setupQuickQuestions();

    // 系统状态
    setupSystemStatus();
}

// 文件上传设置
function setupFileUpload() {
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
    });

    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });

    dropArea.addEventListener('drop', handleDrop, false);
    fileInput.addEventListener('change', () => handleFiles(fileInput.files));

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight() {
        dropArea.classList.add('border-blue-500', 'bg-blue-50');
    }

    function unhighlight() {
        dropArea.classList.remove('border-blue-500', 'bg-blue-50');
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFiles(files);
    }
}

function handleFiles(files) {
    if (!files.length) return;

    const file = files[0];
    const formData = new FormData();
    formData.append('file', file);

    // 显示上传状态
    uploadStatus.classList.remove('hidden');
    uploadStatus.innerHTML = `
        <div class="flex items-center text-blue-600">
            <i class="fas fa-spinner fa-spin mr-2"></i>
            正在上传 "${file.name}"...
        </div>
    `;

    // 开始上传
    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success' && data.upload_id) {
            // 开始监控进度
            startProgressMonitoring(data.upload_id, file.name);
        } else {
            showUploadError(data.message || '上传失败');
        }
    })
    .catch(error => {
        showUploadError(`上传失败: ${error.message}`);
    });
}

function startProgressMonitoring(uploadId, filename) {
    // 显示详细进度界面
    const progressDetail = document.getElementById('upload-progress-detail');
    const progressBar = document.getElementById('progress-bar');
    const progressPercentage = document.getElementById('progress-percentage');
    const currentStep = document.getElementById('current-step');
    const processingSteps = document.getElementById('processing-steps');
    const performanceMetrics = document.getElementById('performance-metrics');

    if (progressDetail) {
        progressDetail.classList.remove('hidden');
        uploadStatus.innerHTML = `
            <div class="flex items-center text-blue-600">
                <i class="fas fa-cog fa-spin mr-2"></i>
                正在处理 "${filename}"...
            </div>
        `;
    }

    // 轮询进度
    const progressInterval = setInterval(() => {
        fetch(`/upload/progress/${uploadId}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.progress) {
                    updateProgressDisplay(data.progress);

                    // 检查是否完成
                    if (data.progress.status === 'completed') {
                        clearInterval(progressInterval);
                        showUploadSuccess(data.progress);
                    } else if (data.progress.status === 'failed') {
                        clearInterval(progressInterval);
                        showUploadError(data.progress.error || '处理失败');
                    }
                }
            })
            .catch(error => {
                console.error('Progress monitoring error:', error);
                clearInterval(progressInterval);
                showUploadError('进度监控失败');
            });
    }, 1000); // 每秒更新一次

    // 设置超时（5分钟）
    setTimeout(() => {
        clearInterval(progressInterval);
        if (progressDetail && !progressDetail.classList.contains('hidden')) {
            showUploadError('处理超时');
        }
    }, 300000);
}

function updateProgressDisplay(progress) {
    const progressBar = document.getElementById('progress-bar');
    const progressPercentage = document.getElementById('progress-percentage');
    const currentStep = document.getElementById('current-step');
    const processingSteps = document.getElementById('processing-steps');
    const performanceMetrics = document.getElementById('performance-metrics');

    // 更新进度条
    if (progressBar) {
        progressBar.style.width = `${progress.progress_percentage || 0}%`;
    }

    // 更新百分比
    if (progressPercentage) {
        progressPercentage.textContent = `${Math.round(progress.progress_percentage || 0)}%`;
    }

    // 更新当前步骤
    if (currentStep) {
        currentStep.textContent = progress.current_step || '处理中...';
    }

    // 更新步骤列表
    if (processingSteps && progress.steps) {
        processingSteps.innerHTML = progress.steps.map((step, index) => {
            let statusIcon = '';
            let statusClass = '';

            switch (step.status) {
                case 'completed':
                    statusIcon = '<i class="fas fa-check-circle text-green-500"></i>';
                    statusClass = 'text-green-600';
                    break;
                case 'running':
                    statusIcon = '<i class="fas fa-spinner fa-spin text-blue-500"></i>';
                    statusClass = 'text-blue-600';
                    break;
                case 'failed':
                    statusIcon = '<i class="fas fa-times-circle text-red-500"></i>';
                    statusClass = 'text-red-600';
                    break;
                default:
                    statusIcon = '<i class="fas fa-circle text-gray-300"></i>';
                    statusClass = 'text-gray-500';
            }

            return `
                <div class="flex items-center space-x-2 text-xs ${statusClass}">
                    ${statusIcon}
                    <span>${step.name}</span>
                    ${step.duration ? `<span class="text-gray-400">(${step.duration.toFixed(2)}s)</span>` : ''}
                </div>
            `;
        }).join('');
    }

    // 更新性能指标
    if (progress.result && progress.result.performance_metrics) {
        const metrics = progress.result.performance_metrics;
        const details = progress.result.processing_details;

        if (performanceMetrics) {
            performanceMetrics.classList.remove('hidden');

            // 基本性能指标
            const processingTime = document.getElementById('processing-time');
            const chunksCount = document.getElementById('chunks-count');
            const embeddingDim = document.getElementById('embedding-dim');
            const processingSpeed = document.getElementById('processing-speed');
            const fileSize = document.getElementById('file-size');
            const storageStatus = document.getElementById('storage-status');

            if (processingTime) processingTime.textContent = `${metrics.total_time?.toFixed(2) || 0}s`;
            if (chunksCount) chunksCount.textContent = progress.result.chunks_count || 0;
            if (embeddingDim) embeddingDim.textContent = details?.embedding_dimension || 0;
            if (processingSpeed) processingSpeed.textContent = `${metrics.chunks_per_second?.toFixed(1) || 0} chunks/s`;
            if (fileSize) fileSize.textContent = formatFileSize(progress.result.file_size || 0);
            if (storageStatus) storageStatus.textContent = progress.result.status === 'success' ? '✓ 已存储' : '✗ 失败';
        }

        // 更新处理详情
        const processingDetails = document.getElementById('processing-details');
        if (processingDetails && details) {
            processingDetails.classList.remove('hidden');

            const detailFileType = document.getElementById('detail-file-type');
            const detailPages = document.getElementById('detail-pages');
            const detailTables = document.getElementById('detail-tables');
            const detailImages = document.getElementById('detail-images');
            const detailOcrPages = document.getElementById('detail-ocr-pages');
            const detailAccuracy = document.getElementById('detail-accuracy');

            if (detailFileType) detailFileType.textContent = details.file_type || '-';
            if (detailPages) detailPages.textContent = details.pages_count || '-';
            if (detailTables) detailTables.textContent = details.tables_count || 0;
            if (detailImages) detailImages.textContent = details.images_count || 0;
            if (detailOcrPages) detailOcrPages.textContent = details.ocr_pages || 0;
            if (detailAccuracy) detailAccuracy.textContent = details.success_rate ? `${details.success_rate.toFixed(1)}%` : '-';
        }
    }
}

function showUploadSuccess(progress) {
    const progressDetail = document.getElementById('upload-progress-detail');
    const result = progress.result;

    uploadStatus.innerHTML = `
        <div class="flex items-center text-green-600">
            <i class="fas fa-check-circle mr-2"></i>
            ${result.message}
        </div>
    `;

    // 显示详细结果
    if (result.processing_details) {
        const details = result.processing_details;
        uploadStatus.innerHTML += `
            <div class="mt-3 p-3 bg-green-50 rounded-lg">
                <div class="text-sm font-medium text-green-800 mb-2">处理结果:</div>
                <div class="grid grid-cols-2 gap-2 text-xs text-green-700">
                    <div>文件类型: ${details.file_type}</div>
                    <div>分块数量: ${result.chunks_count}</div>
                    <div>页面数量: ${details.pages_count}</div>
                    <div>表格数量: ${details.tables_count}</div>
                    <div>图片数量: ${details.images_count}</div>
                    <div>向量维度: ${details.embedding_dimension}</div>
                    <div>处理时间: ${result.processing_time?.toFixed(2)}s</div>
                    <div>平均块大小: ${details.avg_chunk_size} 字符</div>
                </div>
            </div>
        `;
    }

    // 3秒后隐藏详细进度
    setTimeout(() => {
        if (progressDetail) {
            progressDetail.classList.add('hidden');
        }
    }, 3000);
}

function showUploadError(message) {
    const progressDetail = document.getElementById('upload-progress-detail');

    uploadStatus.innerHTML = `
        <div class="flex items-center text-red-600">
            <i class="fas fa-exclamation-circle mr-2"></i>
            ${message}
        </div>
    `;

    if (progressDetail) {
        progressDetail.classList.add('hidden');
    }
}

// 聊天功能设置
function setupChat() {
    sendBtn.addEventListener('click', handleSendMessage);
    queryInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    });
}

function handleSendMessage() {
    const message = queryInput.value.trim();
    if (!message) return;

    // 添加用户消息到聊天界面
    addMessageToChat('user', message);
    queryInput.value = '';

    // 显示正在输入状态
    showTypingIndicator();

    // 获取内容类型过滤
    const contentType = document.querySelector('input[name="contentType"]:checked')?.value || 'all';

    // 发送查询
    fetch('/query', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query: message,
            content_type: contentType
        })
    })
    .then(response => response.json())
    .then(data => {
        hideTypingIndicator();

        if (data.status === 'success') {
            // 添加AI回复到聊天界面
            addMessageToChat('assistant', data.answer, data.latency);

            // 显示参考文档
            if (data.references && data.references.length > 0) {
                showReferences(data.references);
            }

            // 更新会话信息
            if (data.session_info) {
                updateSessionInfo(data.session_info);
            }
        } else {
            addMessageToChat('assistant', `抱歉，查询出现错误：${data.message}`);
        }
    })
    .catch(error => {
        hideTypingIndicator();
        addMessageToChat('assistant', `网络错误：${error.message}`);
    });
}

function addMessageToChat(role, content, latency = null) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `chat-bubble mb-4 ${role === 'user' ? 'ml-8' : 'mr-8'}`;

    const isUser = role === 'user';
    const bgColor = isUser ? 'bg-blue-500 text-white' : 'bg-white border';
    const alignment = isUser ? 'ml-auto' : '';

    messageDiv.innerHTML = `
        <div class="flex ${isUser ? 'justify-end' : 'justify-start'}">
            <div class="max-w-3xl ${bgColor} rounded-lg p-4 shadow-sm ${alignment}">
                <div class="flex items-start space-x-3">
                    ${!isUser ? '<div class="flex-shrink-0"><i class="fas fa-robot text-blue-500 text-lg"></i></div>' : ''}
                    <div class="flex-1">
                        <div class="prose prose-sm max-w-none ${isUser ? 'text-white' : 'text-gray-800'}">
                            ${formatMessage(content)}
                        </div>
                        ${latency ? `<div class="text-xs ${isUser ? 'text-blue-100' : 'text-gray-500'} mt-2">响应时间: ${latency}秒</div>` : ''}
                    </div>
                    ${isUser ? '<div class="flex-shrink-0"><i class="fas fa-user text-white text-lg"></i></div>' : ''}
                </div>
            </div>
        </div>
    `;

    // 清除欢迎消息
    const welcomeMsg = chatMessages.querySelector('.text-center');
    if (welcomeMsg) {
        welcomeMsg.remove();
    }

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function formatMessage(content) {
    // 简单的文本格式化
    return content
        .replace(/\n/g, '<br>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>');
}

function showTypingIndicator() {
    const typingDiv = document.createElement('div');
    typingDiv.id = 'typing-indicator';
    typingDiv.className = 'chat-bubble mb-4 mr-8';
    typingDiv.innerHTML = `
        <div class="flex justify-start">
            <div class="bg-white border rounded-lg p-4 shadow-sm">
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-robot text-blue-500 text-lg"></i>
                    </div>
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                </div>
            </div>
        </div>
    `;

    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

function showReferences(references) {
    if (!referencesSection || !referenceList) return;

    referenceList.innerHTML = '';

    references.forEach((ref, index) => {
        const refDiv = document.createElement('div');
        refDiv.className = 'reference-card bg-white border rounded-lg p-4 hover:shadow-md transition-all';

        const typeClass = ref.type === 'table' ? 'type-table' :
                         ref.type === 'image' ? 'type-image' : 'type-text';
        const typeIcon = ref.type === 'table' ? 'fa-table' :
                        ref.type === 'image' ? 'fa-image' : 'fa-file-text';

        refDiv.innerHTML = `
            <div class="flex items-start justify-between mb-2">
                <div class="flex items-center space-x-2">
                    <span class="content-type-badge ${typeClass}">
                        <i class="fas ${typeIcon} mr-1"></i>
                        ${ref.type === 'table' ? '表格' : ref.type === 'image' ? '图片' : '文本'}
                    </span>
                    <span class="text-sm text-gray-500">相关度: ${(ref.score * 100).toFixed(1)}%</span>
                </div>
                <button class="text-gray-400 hover:text-gray-600" onclick="toggleReference(${index})">
                    <i class="fas fa-chevron-down" id="ref-icon-${index}"></i>
                </button>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-1 mb-3">
                <div class="bg-blue-500 h-1 rounded-full" style="width: ${ref.score * 100}%"></div>
            </div>
            <div class="text-sm text-gray-700 line-clamp-3" id="ref-content-${index}">
                ${ref.text}
            </div>
            ${ref.vector_score ? `
                <div class="mt-2 text-xs text-gray-500">
                    向量相似度: ${(ref.vector_score * 100).toFixed(1)}% |
                    关键词匹配: ${(ref.keyword_score * 100).toFixed(1)}%
                </div>
            ` : ''}
        `;

        referenceList.appendChild(refDiv);
    });

    referencesSection.classList.remove('hidden');
}

function toggleReference(index) {
    const content = document.getElementById(`ref-content-${index}`);
    const icon = document.getElementById(`ref-icon-${index}`);

    if (content.classList.contains('line-clamp-3')) {
        content.classList.remove('line-clamp-3');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    } else {
        content.classList.add('line-clamp-3');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    }
}

// 语音功能设置
function setupVoice() {
    if (!voiceBtn) return;

    voiceBtn.addEventListener('click', toggleVoiceRecording);

    if (stopRecording) {
        stopRecording.addEventListener('click', stopVoiceRecording);
    }

    if (cancelRecording) {
        cancelRecording.addEventListener('click', cancelVoiceRecording);
    }
}

function toggleVoiceRecording() {
    if (isRecording) {
        stopVoiceRecording();
    } else {
        startVoiceRecording();
    }
}

function startVoiceRecording() {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        alert('您的浏览器不支持语音录制功能');
        return;
    }

    navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
            mediaRecorder = new MediaRecorder(stream);
            audioChunks = [];

            mediaRecorder.ondataavailable = event => {
                audioChunks.push(event.data);
            };

            mediaRecorder.onstop = () => {
                const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                processVoiceInput(audioBlob);
                stream.getTracks().forEach(track => track.stop());
            };

            mediaRecorder.start();
            isRecording = true;

            // 显示录音界面
            voiceModal.classList.remove('hidden');
            voiceBtn.innerHTML = '<i class="fas fa-stop text-red-500"></i>';
        })
        .catch(error => {
            console.error('获取麦克风权限失败:', error);
            alert('无法访问麦克风，请检查浏览器权限设置');
        });
}

function stopVoiceRecording() {
    if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        isRecording = false;
        voiceModal.classList.add('hidden');
        voiceBtn.innerHTML = '<i class="fas fa-microphone text-gray-600"></i>';
    }
}

function cancelVoiceRecording() {
    if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        isRecording = false;
        audioChunks = [];
        voiceModal.classList.add('hidden');
        voiceBtn.innerHTML = '<i class="fas fa-microphone text-gray-600"></i>';
    }
}

function processVoiceInput(audioBlob) {
    const formData = new FormData();
    formData.append('audio', audioBlob, 'recording.wav');
    formData.append('format', 'wav');

    // 显示处理状态
    addMessageToChat('system', '正在识别语音...');

    fetch('/speech-to-text', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // 移除处理状态消息
        const systemMsg = chatMessages.querySelector('.chat-bubble:last-child');
        if (systemMsg && systemMsg.textContent.includes('正在识别语音')) {
            systemMsg.remove();
        }

        if (data.status === 'success' && data.text) {
            queryInput.value = data.text;
            // 自动发送识别的文本
            handleSendMessage();
        } else {
            addMessageToChat('system', '语音识别失败，请重试');
        }
    })
    .catch(error => {
        console.error('语音识别错误:', error);
        addMessageToChat('system', '语音识别服务暂时不可用');
    });
}

// 会话管理设置
function setupSession() {
    if (clearSession) {
        clearSession.addEventListener('click', handleClearSession);
    }
}

function loadSessionInfo() {
    fetch('/session/info')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                updateSessionInfo(data.info);
                if (sessionId) {
                    sessionId.textContent = data.session_id.substring(0, 8) + '...';
                }
                currentSessionId = data.session_id;
            }
        })
        .catch(error => {
            console.error('获取会话信息失败:', error);
        });
}

function updateSessionInfo(info) {
    if (info && messageCount) {
        messageCount.textContent = info.message_count || 0;
    }
}

function handleClearSession() {
    if (confirm('确定要清除当前会话吗？这将删除所有对话历史。')) {
        fetch('/session/clear', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // 清除聊天界面
                chatMessages.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-robot text-4xl mb-4 text-blue-400"></i>
                        <p class="text-lg font-medium mb-2">欢迎使用智能文档问答系统</p>
                        <p class="text-sm">上传文档后，您可以向我提问任何相关问题</p>
                    </div>
                `;

                // 隐藏参考文档
                if (referencesSection) {
                    referencesSection.classList.add('hidden');
                }

                // 重置会话信息
                if (messageCount) messageCount.textContent = '0';

                // 显示成功消息
                addMessageToChat('system', '会话已清除');
            }
        })
        .catch(error => {
            console.error('清除会话失败:', error);
            alert('清除会话失败，请重试');
        });
    }
}

// 快捷问题设置
function setupQuickQuestions() {
    quickQuestions.forEach(button => {
        button.addEventListener('click', () => {
            const question = button.textContent.trim();
            queryInput.value = question;
            handleSendMessage();
        });
    });
}

// 系统状态管理
function setupSystemStatus() {
    if (refreshStatusBtn) {
        refreshStatusBtn.addEventListener('click', checkSystemStatus);
    }

    // 初始状态检查
    checkSystemStatus();

    // 定期检查状态（每30秒）
    setInterval(checkSystemStatus, 30000);
}

function checkSystemStatus() {
    fetch('/system/health')
        .then(response => response.json())
        .then(data => {
            updateSystemStatusDisplay(data);
        })
        .catch(error => {
            console.error('System status check failed:', error);
            updateSystemStatusDisplay({
                status: 'error',
                components: {
                    milvus: { status: 'unknown' },
                    redis: { status: 'unknown' },
                    llm: { status: 'unknown' },
                    embedding: { status: 'unknown' }
                }
            });
        });

    // 获取数据库统计信息
    fetch('/database/stats')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' && data.milvus) {
                const docCount = document.getElementById('doc-count');
                if (docCount) {
                    docCount.textContent = data.milvus.num_entities || 0;
                }
            }
        })
        .catch(error => {
            console.error('Database stats check failed:', error);
        });
}

function updateSystemStatusDisplay(healthData) {
    const milvusStatus = document.getElementById('milvus-status');
    const redisStatus = document.getElementById('redis-status');
    const aiStatus = document.getElementById('ai-status');

    if (milvusStatus) {
        updateStatusElement(milvusStatus, healthData.components?.milvus?.status);
    }

    if (redisStatus) {
        updateStatusElement(redisStatus, healthData.components?.redis?.status);
    }

    if (aiStatus) {
        // 综合LLM和嵌入模型状态
        const llmStatus = healthData.components?.llm?.status;
        const embeddingStatus = healthData.components?.embedding?.status;
        const overallAiStatus = (llmStatus === 'healthy' && embeddingStatus === 'healthy') ? 'healthy' :
                               (llmStatus === 'unhealthy' || embeddingStatus === 'unhealthy') ? 'unhealthy' : 'unknown';
        updateStatusElement(aiStatus, overallAiStatus);
    }
}

function updateStatusElement(element, status) {
    element.className = 'text-xs';

    switch (status) {
        case 'healthy':
            element.innerHTML = '<i class="fas fa-circle text-green-500 mr-1"></i>正常';
            element.classList.add('text-green-600');
            break;
        case 'unhealthy':
            element.innerHTML = '<i class="fas fa-circle text-red-500 mr-1"></i>异常';
            element.classList.add('text-red-600');
            break;
        case 'degraded':
            element.innerHTML = '<i class="fas fa-circle text-yellow-500 mr-1"></i>降级';
            element.classList.add('text-yellow-600');
            break;
        case 'not_initialized':
            element.innerHTML = '<i class="fas fa-circle text-gray-500 mr-1"></i>未初始化';
            element.classList.add('text-gray-500');
            break;
        default:
            element.innerHTML = '<i class="fas fa-circle text-gray-400 mr-1"></i>未知';
            element.classList.add('text-gray-500');
    }
}

// 工具函数
function addSystemMessage(message) {
    addMessageToChat('system', message);
}

function formatFileSize(bytes) {
    /**
     * 格式化文件大小显示
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function formatDuration(seconds) {
    /**
     * 格式化时间显示
     * @param {number} seconds - 秒数
     * @returns {string} 格式化后的时间
     */
    if (seconds < 60) {
        return `${seconds.toFixed(1)}秒`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}分${remainingSeconds.toFixed(0)}秒`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}小时${minutes}分钟`;
    }
}

// 添加CSS样式类
const style = document.createElement('style');
style.textContent = `
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .chat-bubble {
        animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
`;
document.head.appendChild(style);

</script>
</body>
</html>
</body>
</html>